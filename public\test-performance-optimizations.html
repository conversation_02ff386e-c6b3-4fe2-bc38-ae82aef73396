<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Optimization Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        .test-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.875rem;
        }
        .status.pass { background: #dcfce7; color: #166534; }
        .status.fail { background: #fef2f2; color: #dc2626; }
        .status.pending { background: #fef3c7; color: #92400e; }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        button:hover {
            background: #1d4ed8;
        }
        .results {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin: 1rem 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .metric:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <h1>🚀 Performance Optimization Test Suite</h1>
    <p>This page tests the performance optimizations implemented for Cheers Marketplace.</p>

    <div class="test-section">
        <h2>1. Snipcart Loading Test</h2>
        <p>Tests that Snipcart only loads when needed (manual loading strategy).</p>
        
        <div class="metric">
            <span>Snipcart Initially Loaded:</span>
            <span class="status" id="snipcart-initial">pending</span>
        </div>
        <div class="metric">
            <span>Load Strategy:</span>
            <span id="load-strategy">-</span>
        </div>
        <div class="metric">
            <span>JavaScript Size Before Cart:</span>
            <span id="js-size-before">-</span>
        </div>
        
        <button onclick="testCartLoading()">Test Cart Loading</button>
        <button onclick="simulateAddToCart()">Simulate Add to Cart</button>
        
        <div class="metric">
            <span>Snipcart Loaded After Click:</span>
            <span class="status" id="snipcart-after">pending</span>
        </div>
        <div class="metric">
            <span>JavaScript Size After Cart:</span>
            <span id="js-size-after">-</span>
        </div>
    </div>

    <div class="test-section">
        <h2>2. CSS Optimization Test</h2>
        <p>Tests that custom minimal CSS is used instead of full Snipcart CSS.</p>
        
        <div class="metric">
            <span>Full Snipcart CSS Loaded:</span>
            <span class="status" id="snipcart-css">pending</span>
        </div>
        <div class="metric">
            <span>Custom Minimal CSS:</span>
            <span class="status" id="custom-css">pending</span>
        </div>
        <div class="metric">
            <span>Total CSS Size:</span>
            <span id="css-size">-</span>
        </div>
    </div>

    <div class="test-section">
        <h2>3. Image Optimization Test</h2>
        <p>Tests image loading and optimization strategies.</p>
        
        <div class="metric">
            <span>Images with WebP Format:</span>
            <span id="webp-count">-</span>
        </div>
        <div class="metric">
            <span>Images with Responsive Srcset:</span>
            <span id="srcset-count">-</span>
        </div>
        <div class="metric">
            <span>Total Image Size:</span>
            <span id="image-size">-</span>
        </div>
        
        <button onclick="testImageOptimization()">Test Image Optimization</button>
    </div>

    <div class="test-section">
        <h2>4. Performance Metrics</h2>
        <p>Real-time performance monitoring results.</p>
        
        <button onclick="runPerformanceTest()">Run Performance Test</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div class="results" id="performance-results">Click "Run Performance Test" to see results...</div>
    </div>

    <script>
        // Test functions
        function testCartLoading() {
            // Check initial state
            document.getElementById('snipcart-initial').textContent = window.Snipcart ? 'Loaded' : 'Not Loaded';
            document.getElementById('snipcart-initial').className = 'status ' + (window.Snipcart ? 'fail' : 'pass');
            
            document.getElementById('load-strategy').textContent = window.SnipcartSettings?.loadStrategy || 'Unknown';
            
            // Measure initial JS size
            const initialJSSize = measureJavaScriptSize();
            document.getElementById('js-size-before').textContent = formatBytes(initialJSSize);
            
            // Simulate cart button click
            if (window.loadSnipcart) {
                window.loadSnipcart().then(() => {
                    setTimeout(() => {
                        document.getElementById('snipcart-after').textContent = window.Snipcart ? 'Loaded' : 'Not Loaded';
                        document.getElementById('snipcart-after').className = 'status ' + (window.Snipcart ? 'pass' : 'fail');
                        
                        const afterJSSize = measureJavaScriptSize();
                        document.getElementById('js-size-after').textContent = formatBytes(afterJSSize);
                    }, 1000);
                });
            }
        }

        function simulateAddToCart() {
            // Create a fake add to cart button and click it
            const fakeButton = document.createElement('button');
            fakeButton.className = 'snipcart-add-item';
            fakeButton.setAttribute('data-item-id', 'test-product');
            fakeButton.setAttribute('data-item-name', 'Test Product');
            fakeButton.setAttribute('data-item-price', '10.00');
            fakeButton.style.display = 'none';
            document.body.appendChild(fakeButton);
            
            fakeButton.click();
            
            setTimeout(() => {
                document.body.removeChild(fakeButton);
            }, 100);
        }

        function testImageOptimization() {
            const images = document.querySelectorAll('img');
            let webpCount = 0;
            let srcsetCount = 0;
            let totalSize = 0;

            images.forEach(img => {
                if (img.src.includes('webp') || img.src.includes('fm=webp')) {
                    webpCount++;
                }
                if (img.srcset) {
                    srcsetCount++;
                }
            });

            // Get image sizes from performance API
            if (performance.getEntriesByType) {
                const resources = performance.getEntriesByType('resource');
                const imageResources = resources.filter(r => 
                    r.name.includes('.jpg') || r.name.includes('.png') || 
                    r.name.includes('.webp') || r.name.includes('unsplash')
                );
                totalSize = imageResources.reduce((total, resource) => {
                    return total + (resource.transferSize || 0);
                }, 0);
            }

            document.getElementById('webp-count').textContent = webpCount;
            document.getElementById('srcset-count').textContent = srcsetCount;
            document.getElementById('image-size').textContent = formatBytes(totalSize);
        }

        function runPerformanceTest() {
            const results = document.getElementById('performance-results');
            results.textContent = 'Running performance test...\n';

            // Add performance monitoring results
            if (window.location.search.includes('debug=performance')) {
                results.textContent += 'Performance monitoring is already active. Check browser console for detailed metrics.\n\n';
            } else {
                results.textContent += 'Add ?debug=performance to URL to enable detailed performance monitoring.\n\n';
            }

            // Basic performance metrics
            if (performance.timing) {
                const timing = performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
                const ttfb = timing.responseStart - timing.navigationStart;

                results.textContent += `Basic Performance Metrics:\n`;
                results.textContent += `- Page Load Time: ${loadTime}ms\n`;
                results.textContent += `- DOM Ready: ${domReady}ms\n`;
                results.textContent += `- Time to First Byte: ${ttfb}ms\n\n`;
            }

            // Resource analysis
            const jsSize = measureJavaScriptSize();
            const cssSize = measureCSSSize();

            results.textContent += `Resource Analysis:\n`;
            results.textContent += `- Total JavaScript: ${formatBytes(jsSize)}\n`;
            results.textContent += `- Total CSS: ${formatBytes(cssSize)}\n`;
            results.textContent += `- Snipcart Status: ${window.Snipcart ? 'Loaded' : 'Not Loaded'}\n`;
            results.textContent += `- Load Strategy: ${window.SnipcartSettings?.loadStrategy || 'Unknown'}\n\n`;

            results.textContent += `Optimization Status:\n`;
            results.textContent += `✅ Manual Snipcart Loading: ${window.SnipcartSettings?.loadStrategy === 'manual' ? 'Active' : 'Inactive'}\n`;
            results.textContent += `✅ Custom CSS: ${document.querySelector('link[href*="snipcart.css"]') ? 'Not Using Custom' : 'Using Custom'}\n`;
            results.textContent += `✅ Performance Monitoring: ${typeof window.PerformanceMonitor !== 'undefined' ? 'Active' : 'Available'}\n`;
        }

        function clearResults() {
            document.getElementById('performance-results').textContent = 'Results cleared. Click "Run Performance Test" to see new results...';
        }

        // Helper functions
        function measureJavaScriptSize() {
            if (!performance.getEntriesByType) return 0;
            const resources = performance.getEntriesByType('resource');
            return resources
                .filter(r => r.name.includes('.js'))
                .reduce((total, resource) => total + (resource.transferSize || 0), 0);
        }

        function measureCSSSize() {
            if (!performance.getEntriesByType) return 0;
            const resources = performance.getEntriesByType('resource');
            return resources
                .filter(r => r.name.includes('.css'))
                .reduce((total, resource) => total + (resource.transferSize || 0), 0);
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Initialize tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Check CSS optimization
            const snipcartCSS = document.querySelector('link[href*="snipcart.css"]');
            document.getElementById('snipcart-css').textContent = snipcartCSS ? 'Found' : 'Not Found';
            document.getElementById('snipcart-css').className = 'status ' + (snipcartCSS ? 'fail' : 'pass');

            const customCSS = document.querySelector('link[href*="snipcart-minimal"]') || 
                             document.querySelector('style[data-astro-cid]');
            document.getElementById('custom-css').textContent = customCSS ? 'Found' : 'Not Found';
            document.getElementById('custom-css').className = 'status ' + (customCSS ? 'pass' : 'fail');

            const cssSize = measureCSSSize();
            document.getElementById('css-size').textContent = formatBytes(cssSize);

            // Auto-run basic tests
            testImageOptimization();
        });
    </script>
</body>
</html>
