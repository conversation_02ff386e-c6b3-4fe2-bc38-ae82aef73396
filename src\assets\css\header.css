/* Header and navigation styles */
.site-header {
  background: var(--light-background);
  color: var(--text);
  box-shadow: var(--shadow-sm);
  padding: 0;
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: 50;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
}

.header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 72px;
  padding: 0 2rem;
}

.site-header .logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: var(--text);
  gap: 0.75rem;
  transition: opacity 0.2s ease;
}

.site-header .logo:hover {
  opacity: 0.8;
}



.site-header .main-nav {
  display: flex;
  gap: 2rem;
  margin-left: auto;
}

.site-header .main-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: -0.01em;
  padding: 0.5rem 1rem;
  border-radius: var(--radius);
  transition: all 0.2s ease;
  position: relative;
}

.site-header .main-nav a:hover {
  color: var(--primary);
  background: var(--border-light);
}

/* Mobile hamburger menu */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  margin-left: auto;
  z-index: 1001;
  border-radius: 4px;
}

.mobile-menu-toggle:hover {
  background: var(--border-light);
}

.hamburger-line {
  width: 24px;
  height: 3px;
  background: #0f172a;
  margin: 3px 0;
  transition: all 0.3s ease;
  border-radius: 2px;
  display: block;
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.menu-open .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .header-flex {
    padding: 0 1.5rem;
    min-height: 64px;
    position: relative;
  }

  /* Show mobile menu toggle */
  .site-header .mobile-menu-toggle {
    display: flex !important;
  }

  /* Hide desktop nav and show mobile nav */
  .site-header .main-nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--light-background);
    border-top: 1px solid var(--border);
    box-shadow: var(--shadow-lg);
    flex-direction: column;
    gap: 0;
    margin: 0;
    padding: 1rem 0;
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
  }

  .site-header .main-nav.nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .site-header .main-nav a {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    border-radius: 0;
    border-bottom: 1px solid var(--border-light);
  }

  .site-header .main-nav a:last-child {
    border-bottom: none;
  }


}

@media (max-width: 480px) {
  .header-flex {
    padding: 0 1rem;
    min-height: 56px;
  }

  .site-header .main-nav a {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }



  .mobile-menu-toggle {
    width: 36px;
    height: 36px;
  }

  .hamburger-line {
    width: 20px;
  }
}
