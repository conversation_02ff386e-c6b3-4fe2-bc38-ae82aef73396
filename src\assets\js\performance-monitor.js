/**
 * Performance Monitoring Script
 * Tracks key performance metrics and reports on optimization effectiveness
 */

class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.thresholds = {
      LCP: 2500, // Largest Contentful Paint should be under 2.5s
      FID: 100,  // First Input Delay should be under 100ms
      CLS: 0.1,  // Cumulative Layout Shift should be under 0.1
      FCP: 1800, // First Contentful Paint should be under 1.8s
      TTFB: 600  // Time to First Byte should be under 600ms
    };
    this.init();
  }

  init() {
    // Wait for page to load before measuring
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.measureMetrics());
    } else {
      this.measureMetrics();
    }
  }

  measureMetrics() {
    // Measure Core Web Vitals
    this.measureLCP();
    this.measureFID();
    this.measureCLS();
    this.measureFCP();
    this.measureTTFB();
    
    // Measure custom metrics
    this.measureResourceLoading();
    this.measureSnipcartPerformance();
    
    // Report after a delay to ensure all metrics are captured
    setTimeout(() => this.reportMetrics(), 3000);
  }

  measureLCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.LCP = Math.round(lastEntry.startTime);
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    }
  }

  measureFID() {
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            this.metrics.FID = Math.round(entry.processingStart - entry.startTime);
          });
        });
        observer.observe({ entryTypes: ['first-input'] });
      } catch (e) {
        // First-input not supported in this environment
        this.metrics.FID = 0;
      }
    }
  }

  measureCLS() {
    if ('PerformanceObserver' in window) {
      try {
        let clsValue = 0;
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.metrics.CLS = Math.round(clsValue * 1000) / 1000;
        });
        observer.observe({ entryTypes: ['layout-shift'] });
      } catch (e) {
        // Layout-shift not supported in this environment
        this.metrics.CLS = 0;
      }
    }
  }

  measureFCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.FCP = Math.round(entry.startTime);
          }
        });
      });
      observer.observe({ entryTypes: ['paint'] });
    }
  }

  measureTTFB() {
    if (performance.timing) {
      this.metrics.TTFB = performance.timing.responseStart - performance.timing.navigationStart;
    } else if (performance.getEntriesByType) {
      const navigationEntries = performance.getEntriesByType('navigation');
      if (navigationEntries.length > 0) {
        this.metrics.TTFB = Math.round(navigationEntries[0].responseStart);
      }
    }
  }

  measureResourceLoading() {
    if (performance.getEntriesByType) {
      const resources = performance.getEntriesByType('resource');
      
      // Track JavaScript loading
      const jsResources = resources.filter(r => r.name.includes('.js'));
      const snipcartJs = jsResources.find(r => r.name.includes('snipcart'));
      
      this.metrics.totalJSSize = jsResources.reduce((total, resource) => {
        return total + (resource.transferSize || 0);
      }, 0);

      if (snipcartJs) {
        this.metrics.snipcartJSSize = snipcartJs.transferSize || 0;
        this.metrics.snipcartLoadTime = Math.round(snipcartJs.responseEnd - snipcartJs.startTime);
      }

      // Track CSS loading
      const cssResources = resources.filter(r => r.name.includes('.css'));
      const snipcartCss = cssResources.find(r => r.name.includes('snipcart'));
      
      this.metrics.totalCSSSize = cssResources.reduce((total, resource) => {
        return total + (resource.transferSize || 0);
      }, 0);

      if (snipcartCss) {
        this.metrics.snipcartCSSSize = snipcartCss.transferSize || 0;
      }

      // Track image loading
      const imageResources = resources.filter(r => 
        r.name.includes('.jpg') || r.name.includes('.png') || 
        r.name.includes('.webp') || r.name.includes('unsplash') ||
        r.name.includes('bunnycdn')
      );
      
      this.metrics.totalImageSize = imageResources.reduce((total, resource) => {
        return total + (resource.transferSize || 0);
      }, 0);

      this.metrics.imageCount = imageResources.length;
    }
  }

  measureSnipcartPerformance() {
    // Track if Snipcart is loaded
    this.metrics.snipcartLoaded = !!window.Snipcart;
    this.metrics.snipcartLoadStrategy = window.SnipcartSettings?.loadStrategy || 'unknown';
    
    // Track cart interactions
    let cartInteractions = 0;
    document.addEventListener('click', (e) => {
      if (e.target.closest('.snipcart-checkout, .snipcart-add-item')) {
        cartInteractions++;
        this.metrics.cartInteractions = cartInteractions;
      }
    });
  }

  reportMetrics() {
    console.group('🚀 Performance Metrics Report');
    
    // Core Web Vitals
    console.group('📊 Core Web Vitals');
    this.logMetric('LCP', this.metrics.LCP, 'ms', this.thresholds.LCP);
    this.logMetric('FID', this.metrics.FID, 'ms', this.thresholds.FID);
    this.logMetric('CLS', this.metrics.CLS, '', this.thresholds.CLS);
    this.logMetric('FCP', this.metrics.FCP, 'ms', this.thresholds.FCP);
    this.logMetric('TTFB', this.metrics.TTFB, 'ms', this.thresholds.TTFB);
    console.groupEnd();

    // Resource Loading
    console.group('📦 Resource Loading');
    console.log(`Total JS Size: ${this.formatBytes(this.metrics.totalJSSize)}`);
    console.log(`Total CSS Size: ${this.formatBytes(this.metrics.totalCSSSize)}`);
    console.log(`Total Image Size: ${this.formatBytes(this.metrics.totalImageSize)}`);
    console.log(`Image Count: ${this.metrics.imageCount}`);
    console.groupEnd();

    // Snipcart Performance
    console.group('🛒 Snipcart Performance');
    console.log(`Load Strategy: ${this.metrics.snipcartLoadStrategy}`);
    console.log(`Snipcart Loaded: ${this.metrics.snipcartLoaded ? '✅' : '❌'}`);
    if (this.metrics.snipcartJSSize) {
      console.log(`Snipcart JS Size: ${this.formatBytes(this.metrics.snipcartJSSize)}`);
      console.log(`Snipcart Load Time: ${this.metrics.snipcartLoadTime}ms`);
    }
    if (this.metrics.snipcartCSSSize) {
      console.log(`Snipcart CSS Size: ${this.formatBytes(this.metrics.snipcartCSSSize)}`);
    } else {
      console.log('Snipcart CSS: Using minimal custom CSS ✅');
    }
    console.groupEnd();

    // Performance Score
    const score = this.calculatePerformanceScore();
    console.log(`🎯 Overall Performance Score: ${score}/100`);
    
    console.groupEnd();

    // Store metrics for potential analytics
    if (typeof gtag !== 'undefined') {
      this.sendToAnalytics();
    }
  }

  logMetric(name, value, unit, threshold) {
    if (value === undefined || value === null) {
      console.log(`%c${name}: Not available in this environment`, 'color: orange');
      return;
    }
    const status = value <= threshold ? '✅' : '❌';
    const color = value <= threshold ? 'color: green' : 'color: red';
    console.log(`%c${name}: ${value}${unit} ${status}`, color);
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  calculatePerformanceScore() {
    let score = 100;
    
    // Deduct points for poor Core Web Vitals
    if (this.metrics.LCP > this.thresholds.LCP) score -= 20;
    if (this.metrics.FID > this.thresholds.FID) score -= 15;
    if (this.metrics.CLS > this.thresholds.CLS) score -= 15;
    if (this.metrics.FCP > this.thresholds.FCP) score -= 15;
    if (this.metrics.TTFB > this.thresholds.TTFB) score -= 10;

    // Bonus points for optimizations
    if (this.metrics.snipcartLoadStrategy === 'manual') score += 5;
    if (!this.metrics.snipcartCSSSize) score += 5; // Using custom CSS

    return Math.max(0, Math.min(100, score));
  }

  sendToAnalytics() {
    // Send key metrics to Google Analytics
    gtag('event', 'performance_metrics', {
      'custom_parameter_1': this.metrics.LCP,
      'custom_parameter_2': this.metrics.FID,
      'custom_parameter_3': this.metrics.CLS,
      'value': this.calculatePerformanceScore()
    });
  }
}

// Initialize performance monitoring
if (typeof window !== 'undefined') {
  new PerformanceMonitor();
}
