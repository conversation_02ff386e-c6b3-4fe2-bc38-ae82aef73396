# Admin Panel Setup Guide

## Overview

The admin panel sync functionality has been restored and now works with the static site generation. The sync process uses Cloudflare Pages Functions to handle GitHub commits and cache purging.

## Environment Variables Setup

### 1. Required Variables (for sync functionality)

Set these in your **Cloudflare Pages dashboard** under Settings > Environment Variables:

```bash
GITHUB_TOKEN=your_github_personal_access_token
GITHUB_OWNER=your_github_username  
GITHUB_REPO=your_repository_name
```

### 2. Optional Variables (for enhanced functionality)

```bash
# For automatic cache purging
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# For automatic build triggering
CLOUDFLARE_BUILD_HOOK_URL=your_cloudflare_pages_build_hook_url
```

## GitHub Token Setup

1. Go to GitHub Settings > Developer settings > Personal access tokens
2. Create a new token with these permissions:
   - `repo` (Full control of private repositories)
   - `contents:write` (Write access to repository contents)
3. Copy the token and add it as `GITHUB_TOKEN` in Cloudflare Pages

## How It Works

### Sync Process ✅ OPTIMIZED

1. **Admin Panel Change** → Product added/updated/deleted locally
2. **Sync Button** → Calls `/api/sync-products` Cloudflare Pages Function
3. **Single GitHub Commit** → Updates `src/data/products.json` only (prevents multiple builds)
4. **Cache Purge** → Clears Cloudflare cache for affected URLs (if configured)
5. **Build Trigger** → Triggers ONE deployment (if build hook configured)
6. **Build Process** → Automatically copies `src/data/products.json` to `public/data/products.json`
7. **Site Update** → New content is live

### File Updates

The sync process now makes **only ONE commit** to prevent multiple builds:
- `src/data/products.json` - Source of truth, committed to GitHub
- `public/data/products.json` - Generated during build process for runtime access

This ensures efficient syncing with only one build per change.

## Troubleshooting

### "JSON.parse: unexpected end of data" Error ✅ FIXED

This error was caused by missing API endpoints after converting to static generation. **This has been resolved** by creating the proper Cloudflare Pages Function.

If you still see this error, check:

1. **Environment Variables**: Ensure all required variables are set in Cloudflare Pages
2. **GitHub Token**: Verify the token has correct permissions
3. **Repository Access**: Ensure the token can access the specified repository
4. **Deployment**: Make sure the latest code with the Functions is deployed

### Sync Button Not Working

1. Check browser console for JavaScript errors
2. Verify the `/api/sync-products` endpoint is accessible
3. Test the endpoint manually: `POST /api/sync-products` with products data

### Changes Not Appearing

1. **Immediate**: Check if `public/data/products.json` was updated
2. **After Build**: Check if `src/data/products.json` was updated in GitHub
3. **Cache**: Try hard refresh (Ctrl+F5) or check if cache purging is working

## Testing

Use the debug page at `/debug-sync/` to test:
- Products loading
- API endpoint availability
- Sync functionality

## Local Development

For local development with Wrangler:

1. Copy `.env.example` to `.env.local`
2. Fill in your actual values
3. Run `npx wrangler pages dev dist` for local function testing

Note: The admin panel sync will only work when deployed to Cloudflare Pages, not in local Astro preview mode.
