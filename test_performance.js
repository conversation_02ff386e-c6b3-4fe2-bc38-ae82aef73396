// Performance Test Script
// Run this in the browser console to test the optimizations

console.log('🚀 Testing Performance Optimizations...\n');

// Test 1: Check if critical CSS is loaded
function testCriticalCSS() {
  console.log('1. Testing Critical CSS Loading...');
  const criticalStyles = document.querySelector('style');
  if (criticalStyles && criticalStyles.textContent.includes('--primary')) {
    console.log('✅ Critical CSS is inlined and loaded');
  } else {
    console.log('❌ Critical CSS not found');
  }
}

// Test 2: Check if non-critical CSS is loaded asynchronously
function testNonCriticalCSS() {
  console.log('\n2. Testing Non-Critical CSS Loading...');
  const nonCriticalLink = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))
    .find(link => link.href.includes('non-critical'));
  
  if (nonCriticalLink) {
    console.log('✅ Non-critical CSS is loaded asynchronously');
  } else {
    console.log('⚠️ Non-critical CSS link not found (may be loaded via script)');
  }
}

// Test 3: Check Snipcart lazy loading
function testSnipcartLazyLoading() {
  console.log('\n3. Testing Snipcart Lazy Loading...');
  
  if (window.loadSnipcart) {
    console.log('✅ Snipcart lazy loading function is available');
  } else {
    console.log('❌ Snipcart lazy loading function not found');
  }
  
  const snipcartScript = document.querySelector('script[src*="snipcart"]');
  if (!snipcartScript) {
    console.log('✅ Snipcart script is not loaded initially (good for lazy loading)');
  } else {
    console.log('⚠️ Snipcart script is loaded immediately');
  }
}

// Test 4: Check image optimization
function testImageOptimization() {
  console.log('\n4. Testing Image Optimization...');
  
  const preloadedImages = document.querySelectorAll('link[rel="preload"][as="image"]');
  if (preloadedImages.length > 0) {
    console.log(`✅ ${preloadedImages.length} critical images are preloaded`);
  } else {
    console.log('❌ No critical images are preloaded');
  }
  
  const images = document.querySelectorAll('img');
  let optimizedImages = 0;
  images.forEach(img => {
    if (img.src.includes('w=800') || img.src.includes('webp') || img.src.includes('auto=format')) {
      optimizedImages++;
    }
  });
  
  if (optimizedImages > 0) {
    console.log(`✅ ${optimizedImages} images have optimization parameters`);
  } else {
    console.log('⚠️ No optimized images found');
  }
}

// Test 5: Check heading hierarchy
function testHeadingHierarchy() {
  console.log('\n5. Testing Heading Hierarchy...');
  
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
  const headingLevels = Array.from(headings).map(h => parseInt(h.tagName.charAt(1)));
  
  let hierarchyValid = true;
  let currentLevel = 0;
  
  for (let level of headingLevels) {
    if (currentLevel === 0) {
      if (level !== 1) {
        hierarchyValid = false;
        break;
      }
    } else if (level > currentLevel + 1) {
      hierarchyValid = false;
      break;
    }
    currentLevel = Math.max(currentLevel, level);
  }
  
  if (hierarchyValid) {
    console.log('✅ Heading hierarchy is valid');
  } else {
    console.log('❌ Heading hierarchy has issues');
  }
  
  console.log(`   Found headings: ${headingLevels.join(', ')}`);
}

// Test 6: Check modern JavaScript features
function testModernJavaScript() {
  console.log('\n6. Testing Modern JavaScript Support...');
  
  try {
    // Test ES2022 features
    const testClass = class {
      #privateField = 'test';
      static {
        // Static initialization block (ES2022)
      }
    };
    console.log('✅ ES2022 features are supported');
  } catch (e) {
    console.log('⚠️ Some ES2022 features may not be supported');
  }
}

// Test 7: Performance metrics
function testPerformanceMetrics() {
  console.log('\n7. Testing Performance Metrics...');
  
  if (window.performance && window.performance.getEntriesByType) {
    const navigation = window.performance.getEntriesByType('navigation')[0];
    if (navigation) {
      console.log(`   DOM Content Loaded: ${Math.round(navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart)}ms`);
      console.log(`   Load Complete: ${Math.round(navigation.loadEventEnd - navigation.loadEventStart)}ms`);
    }
    
    // Check for custom performance marks
    const marks = window.performance.getEntriesByType('mark');
    if (marks.length > 0) {
      console.log(`✅ Found ${marks.length} performance marks`);
      marks.forEach(mark => console.log(`   - ${mark.name}`));
    }
  }
}

// Run all tests
function runAllTests() {
  testCriticalCSS();
  testNonCriticalCSS();
  testSnipcartLazyLoading();
  testImageOptimization();
  testHeadingHierarchy();
  testModernJavaScript();
  testPerformanceMetrics();
  
  console.log('\n🎉 Performance testing complete!');
  console.log('\nTo test Snipcart lazy loading, hover over or click an "Add to Cart" button.');
}

// Auto-run tests after a short delay to ensure page is loaded
setTimeout(runAllTests, 2000);

// Export for manual testing
window.testPerformance = runAllTests;
