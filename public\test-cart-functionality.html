<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Functionality Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }
        .test-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        .status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: 600;
            font-size: 0.875rem;
        }
        .status.pass { background: #dcfce7; color: #166534; }
        .status.fail { background: #fef2f2; color: #dc2626; }
        .status.pending { background: #fef3c7; color: #92400e; }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        button:hover {
            background: #1d4ed8;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .cart-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 1rem;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            margin: 1rem 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🛒 Cart Functionality Test</h1>
    <p>This page tests the cart functionality to ensure it doesn't freeze or crash.</p>

    <div class="test-section">
        <h2>1. Cart Button Test</h2>
        <p>Test the cart button to ensure it loads Snipcart without freezing.</p>
        
        <button id="test-cart-btn" class="cart-button snipcart-checkout">
            <span id="cart-text">Open Cart</span>
            <div id="cart-spinner" class="loading-spinner" style="display: none;"></div>
        </button>
        
        <div style="margin-top: 1rem;">
            <strong>Status:</strong> <span id="cart-status" class="status pending">Ready to test</span>
        </div>
    </div>

    <div class="test-section">
        <h2>2. Add to Cart Test</h2>
        <p>Test adding a product to cart.</p>
        
        <button 
            class="snipcart-add-item"
            data-item-id="test-product-1"
            data-item-name="Test Product"
            data-item-price="19.99"
            data-item-description="A test product for cart functionality"
            data-item-image="/images/product-placeholder.svg"
        >
            Add Test Product to Cart - $19.99
        </button>
        
        <div style="margin-top: 1rem;">
            <strong>Status:</strong> <span id="add-status" class="status pending">Ready to test</span>
        </div>
    </div>

    <div class="test-section">
        <h2>3. Account Button Test</h2>
        <p>Test the customer account functionality.</p>
        
        <button class="snipcart-customer-signin">
            My Account
        </button>
        
        <div style="margin-top: 1rem;">
            <strong>Status:</strong> <span id="account-status" class="status pending">Ready to test</span>
        </div>
    </div>

    <div class="test-section">
        <h2>4. System Status</h2>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div>
                <strong>Snipcart Loaded:</strong> <span id="snipcart-loaded">❌</span>
            </div>
            <div>
                <strong>Load Strategy:</strong> <span id="load-strategy">-</span>
            </div>
            <div>
                <strong>Page Responsive:</strong> <span id="page-responsive">✅</span>
            </div>
            <div>
                <strong>Console Errors:</strong> <span id="console-errors">0</span>
            </div>
        </div>
        
        <button onclick="refreshStatus()">Refresh Status</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>5. Event Log</h2>
        <div id="event-log" class="log">Waiting for events...</div>
    </div>

    <script>
        let errorCount = 0;
        let eventLog = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            eventLog.push(logMessage);
            
            const logElement = document.getElementById('event-log');
            logElement.textContent = eventLog.slice(-20).join('\n'); // Keep last 20 messages
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(logMessage);
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${status}`;
        }

        function refreshStatus() {
            // Check Snipcart status
            const snipcartLoaded = !!(window.Snipcart && window.Snipcart.api);
            document.getElementById('snipcart-loaded').textContent = snipcartLoaded ? '✅' : '❌';
            
            // Check load strategy
            const loadStrategy = window.SnipcartSettings?.loadStrategy || 'unknown';
            document.getElementById('load-strategy').textContent = loadStrategy;
            
            // Check page responsiveness
            const responsive = document.body.style.pointerEvents !== 'none';
            document.getElementById('page-responsive').textContent = responsive ? '✅' : '❌';
            
            // Update error count
            document.getElementById('console-errors').textContent = errorCount;
            
            log('Status refreshed');
        }

        function clearLog() {
            eventLog = [];
            document.getElementById('event-log').textContent = 'Log cleared...';
        }

        // Monitor for errors
        window.addEventListener('error', function(e) {
            errorCount++;
            log(`ERROR: ${e.message} at ${e.filename}:${e.lineno}`);
            refreshStatus();
        });

        // Monitor for unhandled promise rejections
        window.addEventListener('unhandledrejection', function(e) {
            errorCount++;
            log(`PROMISE REJECTION: ${e.reason}`);
            refreshStatus();
        });

        // Monitor cart button clicks
        document.addEventListener('click', function(e) {
            if (e.target.closest('.snipcart-checkout')) {
                log('Cart button clicked');
                updateStatus('cart-status', 'pending', 'Loading...');
                
                // Set a timeout to check if the page freezes
                const freezeCheck = setTimeout(() => {
                    log('WARNING: Page may have frozen - no response after 5 seconds');
                    updateStatus('cart-status', 'fail', 'Possible freeze detected');
                }, 5000);
                
                // Clear the timeout if Snipcart loads successfully
                const checkSnipcart = () => {
                    if (window.Snipcart && window.Snipcart.api) {
                        clearTimeout(freezeCheck);
                        log('Snipcart loaded successfully');
                        updateStatus('cart-status', 'pass', 'Cart loaded successfully');
                    } else {
                        setTimeout(checkSnipcart, 100);
                    }
                };
                checkSnipcart();
            }
            
            if (e.target.closest('.snipcart-add-item')) {
                log('Add to cart button clicked');
                updateStatus('add-status', 'pending', 'Adding to cart...');
                
                setTimeout(() => {
                    if (window.Snipcart && window.Snipcart.api) {
                        log('Product added to cart successfully');
                        updateStatus('add-status', 'pass', 'Product added');
                    } else {
                        log('Failed to add product - Snipcart not loaded');
                        updateStatus('add-status', 'fail', 'Failed to add');
                    }
                }, 2000);
            }
            
            if (e.target.closest('.snipcart-customer-signin')) {
                log('Account button clicked');
                updateStatus('account-status', 'pending', 'Loading account...');
                
                setTimeout(() => {
                    if (window.Snipcart && window.Snipcart.api) {
                        log('Account modal opened successfully');
                        updateStatus('account-status', 'pass', 'Account loaded');
                    } else {
                        log('Failed to open account - Snipcart not loaded');
                        updateStatus('account-status', 'fail', 'Failed to load');
                    }
                }, 2000);
            }
        });

        // Monitor Snipcart events
        document.addEventListener('snipcart.ready', function() {
            log('Snipcart ready event fired');
            refreshStatus();
        });

        document.addEventListener('snipcart.item.added', function(e) {
            log(`Item added to cart: ${e.detail.item.name}`);
        });

        // Initial status check
        document.addEventListener('DOMContentLoaded', function() {
            log('Page loaded, starting tests...');
            refreshStatus();
            
            // Check if loadSnipcart function is available
            if (typeof window.loadSnipcart === 'function') {
                log('loadSnipcart function is available');
            } else {
                log('WARNING: loadSnipcart function not found');
            }
        });

        // Periodic responsiveness check
        setInterval(() => {
            const now = Date.now();
            setTimeout(() => {
                const delay = Date.now() - now;
                if (delay > 100) {
                    log(`WARNING: Page lag detected (${delay}ms delay)`);
                }
            }, 0);
        }, 2000);
    </script>
</body>
</html>
