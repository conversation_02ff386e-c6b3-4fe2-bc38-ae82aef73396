// Modern Admin Panel JavaScript - Extracted for better maintainability
class ModernAdminPanel {
  constructor(initialProducts, initialCategories = []) {
    this.products = [...initialProducts];
    this.filteredProducts = [...this.products];

    // Track manually created categories (even if no products use them yet)
    // Load from saved categories data
    this.createdCategories = new Set(initialCategories);

    this.categories = this.getCategories();
    this.currentPage = 1;
    this.itemsPerPage = 20;
    this.currentFilter = '';
    this.currentSearch = '';
    this.currentSort = 'newest';
    this.editingProduct = null;
    this.editingCategory = null;
    this.keyPoints = [];
    this.featuredImageUrl = null; // Track the featured image URL

    // Pagination properties
    this.itemsPerPage = 20; // Default items per page for admin

    // Track unsaved changes
    this.hasUnsavedChanges = false;
    this.lastSyncedProducts = [...initialProducts];
    this.lastSyncedCategories = [...new Set(initialProducts.map(p => p.category))].filter(Boolean).sort();

    this.init();
  }
  
  init() {
    this.checkAdminAccess();
    this.bindEvents();
    this.renderStats();
    this.renderProducts();
    this.initializeForm();
    this.checkGitHubStatus();
    this.updateSyncButtonState();
  }
  
  checkAdminAccess() {
    const isAdmin = new URLSearchParams(window.location.search).get('admin') === '1';
    const adminContent = document.getElementById('admin-content');
    const notAdmin = document.getElementById('not-admin');
    
    if (isAdmin) {
      adminContent.style.display = 'block';
      notAdmin.style.display = 'none';
    } else {
      adminContent.style.display = 'none';
      notAdmin.style.display = 'block';
    }
  }

  bindEvents() {
    // Tab navigation
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tab = e.target.closest('.admin-tab-btn').dataset.tab;
        this.switchTab(tab);
      });
    });

    // Filters and search
    document.getElementById('admin-category-filter').addEventListener('change', (e) => {
      this.currentFilter = e.target.value;
      this.currentPage = 1;
      this.filterAndRenderProducts();
    });

    document.getElementById('admin-sort-by').addEventListener('change', (e) => {
      this.currentSort = e.target.value;
      this.filterAndRenderProducts();
    });

    document.getElementById('admin-search').addEventListener('input', (e) => {
      this.currentSearch = e.target.value.toLowerCase();
      this.currentPage = 1;
      this.filterAndRenderProducts();
    });

    // Product actions
    document.getElementById('add-product-btn').addEventListener('click', () => {
      this.editingProduct = null;
      this.switchTab('form');
      this.resetForm();
    });

    document.getElementById('save-product').addEventListener('click', () => {
      this.saveProduct();
    });

    document.getElementById('cancel-form').addEventListener('click', () => {
      this.switchTab('list');
      this.resetForm();
    });

    // Category actions
    document.getElementById('add-category-btn').addEventListener('click', () => {
      this.showCategoryForm();
    });

    document.getElementById('save-category').addEventListener('click', () => {
      this.saveCategory();
    });

    document.getElementById('cancel-category').addEventListener('click', () => {
      this.hideCategoryForm();
    });

    // Sync actions
    document.getElementById('sync-products').addEventListener('click', () => {
      this.syncProducts();
    });

    document.getElementById('test-github').addEventListener('click', () => {
      this.testGitHub();
    });

    // Items per page selector
    const itemsPerPageSelect = document.getElementById('admin-items-per-page');
    if (itemsPerPageSelect) {
      itemsPerPageSelect.addEventListener('change', (e) => {
        this.itemsPerPage = parseInt(e.target.value);
        this.currentPage = 1; // Reset to first page
        this.renderProducts();
      });
    }
  }

  switchTab(tab) {
    // Update tab buttons
    document.querySelectorAll('.admin-tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tab}"]`).classList.add('active');

    // Update sections
    document.querySelectorAll('.admin-section').forEach(section => {
      section.classList.remove('active');
    });
    document.getElementById(`section-${tab}`).classList.add('active');

    if (tab === 'form') {
      this.initializeForm();
    } else if (tab === 'categories') {
      this.renderCategories();
    }
  }

  getCategories() {
    // Get categories from products
    const productCategories = [...new Set(this.products.map(p => p.category))].filter(Boolean);

    // Combine with manually created categories
    const allCategories = [...new Set([...productCategories, ...this.createdCategories])];

    return allCategories.sort();
  }

  renderStats() {
    document.getElementById('total-products').textContent = this.products.length;
    document.getElementById('visible-products').textContent = this.filteredProducts.length;
    document.getElementById('categories-count').textContent = this.categories.length;
  }

  filterAndRenderProducts() {
    let filtered = [...this.products];

    // Apply category filter
    if (this.currentFilter) {
      filtered = filtered.filter(p => p.category === this.currentFilter);
    }

    // Apply search filter
    if (this.currentSearch) {
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(this.currentSearch) ||
        p.description.toLowerCase().includes(this.currentSearch) ||
        p.category.toLowerCase().includes(this.currentSearch)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (this.currentSort) {
        case 'price':
          return parseFloat(a.price) - parseFloat(b.price);
        case 'category':
          return a.category.localeCompare(b.category);
        case 'newest':
          return new Date(b.dateAdded || 0) - new Date(a.dateAdded || 0);
        default: // name
          return a.name.localeCompare(b.name);
      }
    });

    this.filteredProducts = filtered;
    this.renderProducts();
    this.renderStats();
  }

  renderProducts() {
    const tbody = document.getElementById('products-tbody');
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    const pageProducts = this.filteredProducts.slice(startIndex, endIndex);

    if (pageProducts.length === 0) {
      document.getElementById('empty-state').style.display = 'block';
      document.querySelector('.admin-table-wrapper').style.display = 'none';
      document.getElementById('pagination').style.display = 'none';
    } else {
      document.getElementById('empty-state').style.display = 'none';
      document.querySelector('.admin-table-wrapper').style.display = 'block';
      document.getElementById('pagination').style.display = 'flex';
    }

    tbody.innerHTML = pageProducts.map(product => this.renderProductRow(product)).join('');
    this.renderPagination();
  }

  renderProductRow(product) {
    const isNew = !this.lastSyncedProducts.find(p => p.id === product.id);
    const isModified = this.lastSyncedProducts.find(p => 
      p.id === product.id && JSON.stringify(p) !== JSON.stringify(product)
    );
    
    const rowClass = isNew ? 'product-new' : (isModified ? 'product-modified' : '');
    const changeIndicator = isNew ? '🆕' : (isModified ? '📝' : '');
    
    return `
      <tr class="product-row ${rowClass}">
        <td class="product-name-cell">
          <div class="product-name-container">
            <span class="change-indicator">${changeIndicator}</span>
            <strong>${product.name}</strong>
            <div class="product-description-preview">${product.description.substring(0, 100)}...</div>
          </div>
        </td>
        <td><span class="category-badge">${product.category}</span></td>
        <td>$${parseFloat(product.price).toFixed(2)}</td>
        <td class="images-count">${product.images ? product.images.length : 0} images</td>
        <td>
          <div class="status-indicators">
            <span class="status-badge good">Good</span>
            ${product.defects ? '<span class="status-badge defects">Defects</span>' : ''}
          </div>
        </td>
        <td>
          <div class="action-buttons">
            <button class="btn-edit" onclick="adminPanel.editProduct('${product.id}')" title="Edit">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
              </svg>
            </button>
            <button class="btn-delete" onclick="adminPanel.deleteProduct('${product.id}')" title="Delete">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
              </svg>
            </button>
          </div>
        </td>
      </tr>
    `;
  }

  renderPagination() {
    const totalPages = Math.ceil(this.filteredProducts.length / this.itemsPerPage);
    const pagination = document.getElementById('pagination');
    
    if (totalPages <= 1) {
      pagination.innerHTML = '';
      return;
    }

    let paginationHTML = '';
    
    // Previous button
    if (this.currentPage > 1) {
      paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage - 1})">‹</button>`;
    }
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
      if (i === this.currentPage) {
        paginationHTML += `<button class="page-btn active">${i}</button>`;
      } else {
        paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${i})">${i}</button>`;
      }
    }
    
    // Next button
    if (this.currentPage < totalPages) {
      paginationHTML += `<button class="page-btn" onclick="adminPanel.goToPage(${this.currentPage + 1})">›</button>`;
    }
    
    pagination.innerHTML = paginationHTML;

    // Update admin pagination info
    this.updateAdminPaginationInfo();
  }

  updateAdminPaginationInfo() {
    const paginationInfo = document.getElementById('admin-pagination-info');
    if (!paginationInfo) return;

    const totalProducts = this.filteredProducts.length;
    if (totalProducts === 0) {
      paginationInfo.textContent = 'No products found';
      return;
    }

    const startIndex = (this.currentPage - 1) * this.itemsPerPage + 1;
    const endIndex = Math.min(this.currentPage * this.itemsPerPage, totalProducts);

    paginationInfo.textContent = `Showing ${startIndex}-${endIndex} of ${totalProducts} products`;
  }

  goToPage(page) {
    this.currentPage = page;
    this.renderProducts();
  }

  initializeForm() {
    // Initialize the product form with all necessary fields
    const form = document.getElementById('product-form');
    if (!form) return;

    form.innerHTML = `
      <div class="compact-form-wrapper">
        <!-- Compact Form Grid Layout -->

        <div class="compact-form-grid">
          <!-- Row 1: Basic Info -->
          <div class="form-section">
            <h3 class="section-title">Basic Information</h3>
            <div class="form-row">
              <div class="form-group">
                <label for="product-name">Product Name <span class="required">*</span></label>
                <input type="text" id="product-name" name="name" required placeholder="Enter product name" />
              </div>
              <div class="form-group">
                <label for="product-price">Price <span class="required">*</span></label>
                <div class="price-wrapper">
                  <span class="currency">$</span>
                  <input type="number" id="product-price" name="price" required placeholder="0.00" step="0.01" min="0" />
                </div>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="product-category">Category <span class="required">*</span></label>
                <select id="product-category" name="category" required>
                  <option value="">Select category</option>
                  ${this.getCategories().map(category =>
                    `<option value="${category}">${category}</option>`
                  ).join('')}
                </select>
              </div>
              <div class="form-group">
                <label for="product-condition">Condition <span class="required">*</span></label>
                <select id="product-condition" name="condition" required>
                  <option value="">Select condition</option>
                  <option value="Poor">Poor</option>
                  <option value="Fair">Fair</option>
                  <option value="Good">Good</option>
                  <option value="Excellent">Excellent</option>
                  <option value="New">New</option>
                </select>
              </div>
            </div>
            <div class="form-group full-width">
              <label for="product-description">Description <span class="required">*</span></label>
              <textarea id="product-description" name="description" required placeholder="Describe your product..." rows="3"></textarea>
            </div>
          </div>

          <!-- Row 2: Images & Features -->
          <div class="form-section">
            <h3 class="section-title">Images & Features</h3>
            <div class="form-row">
              <div class="form-group">
                <label for="product-images">Product Images</label>

                <!-- Image Upload Area -->
                <div class="image-upload-container">
                  <div class="upload-area" id="image-upload-area">
                    <div class="upload-content">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                      </svg>
                      <h4>Upload Images</h4>
                      <p>Drag and drop images here, or click to select</p>
                      <button type="button" class="btn-upload">Choose Files</button>
                    </div>
                    <input type="file" id="image-file-input" multiple accept="image/*" style="display: none;">
                  </div>

                  <!-- Upload Progress -->
                  <div class="upload-progress" id="upload-progress" style="display: none;">
                    <div class="progress-bar">
                      <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <span class="progress-text" id="progress-text">Uploading...</span>
                  </div>

                  <!-- Image Previews -->
                  <div class="image-previews" id="image-previews"></div>
                </div>

                <!-- Hidden field to store uploaded image URLs -->
                <textarea id="product-images" name="images" style="display: none;"></textarea>
              </div>
              <div class="form-group">
                <label>Key Features</label>
                <div class="features-area" id="features-display">
                  <!-- Features will be displayed here -->
                </div>
                <div class="feature-input">
                  <input type="text" id="feature-label" placeholder="Feature (e.g., Material)" />
                  <input type="text" id="feature-value" placeholder="Value (e.g., Cotton)" />
                  <button type="button" id="add-feature" class="btn-add-feature">+</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Row 3: Additional Notes -->
          <div class="form-section">
            <h3 class="section-title">Additional Notes</h3>
            <div class="form-group full-width">
              <label for="product-defects">Defects/Condition Notes</label>
              <textarea id="product-defects" name="defects" placeholder="Describe any defects, wear, or condition issues..." rows="3"></textarea>
              <small class="help-text warning">Be honest about any defects to maintain customer trust.</small>
            </div>
          </div>
        </div>
      </div>
    `;

    // Bind form events
    this.bindFormEvents();
  }

  bindFormEvents() {
    // Add feature functionality
    const addFeatureBtn = document.getElementById('add-feature');
    if (addFeatureBtn) {
      addFeatureBtn.addEventListener('click', () => {
        this.addKeyPoint();
      }, { passive: true });
    }

    // Enter key support for adding features
    const featureLabel = document.getElementById('feature-label');
    const featureValue = document.getElementById('feature-value');

    if (featureLabel && featureValue) {
      [featureLabel, featureValue].forEach(input => {
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            this.addKeyPoint();
          }
        });
      });
    }

    // Bind image upload events
    this.bindImageUploadEvents();
  }

  bindImageUploadEvents() {
    const uploadArea = document.getElementById('image-upload-area');
    const fileInput = document.getElementById('image-file-input');
    const uploadButton = uploadArea?.querySelector('.btn-upload');

    if (!uploadArea || !fileInput || !uploadButton) return;

    // Click to select files
    uploadButton.addEventListener('click', () => {
      fileInput.click();
    }, { passive: true });

    uploadArea.addEventListener('click', (e) => {
      if (e.target === uploadArea || e.target.closest('.upload-content')) {
        fileInput.click();
      }
    }, { passive: true });

    // File input change
    fileInput.addEventListener('change', (e) => {
      const files = Array.from(e.target.files || []);
      if (files.length > 0) {
        this.handleImageUpload(files);
      }
    }, { passive: true });

    // Drag and drop
    uploadArea.addEventListener('dragover', (e) => {
      e.preventDefault();
      uploadArea.classList.add('drag-over');
    });

    uploadArea.addEventListener('dragleave', (e) => {
      e.preventDefault();
      if (!uploadArea.contains(e.relatedTarget)) {
        uploadArea.classList.remove('drag-over');
      }
    });

    uploadArea.addEventListener('drop', (e) => {
      e.preventDefault();
      uploadArea.classList.remove('drag-over');

      const files = Array.from(e.dataTransfer?.files || []).filter(file =>
        file.type.startsWith('image/')
      );

      if (files.length > 0) {
        this.handleImageUpload(files);
      }
    });
  }

  async handleImageUpload(files) {
    const progressContainer = document.getElementById('upload-progress');
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');
    const previewsContainer = document.getElementById('image-previews');

    if (!progressContainer || !progressFill || !progressText || !previewsContainer) return;

    try {
      // Show progress
      progressContainer.style.display = 'block';
      progressText.textContent = 'Preparing upload...';
      progressFill.style.width = '0%';

      // Create form data
      const formData = new FormData();
      files.forEach(file => {
        formData.append('images', file);
      });

      // Upload files
      progressText.textContent = 'Uploading images...';
      progressFill.style.width = '50%';

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        body: formData
      });

      // Check if response is ok and has content
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      // Check if response has content
      const responseText = await response.text();
      if (!responseText) {
        throw new Error('Empty response from server');
      }

      // Try to parse JSON
      let result;
      try {
        result = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Response text:', responseText);
        throw new Error(`Invalid JSON response: ${parseError.message}`);
      }

      if (result.success && result.uploaded.length > 0) {
        // Update progress
        progressFill.style.width = '100%';
        progressText.textContent = `Successfully uploaded ${result.uploaded.length} image(s)`;

        // Add URLs to textarea
        const textarea = document.getElementById('product-images');
        const currentUrls = textarea.value.trim();
        const newUrls = result.uploaded.map(item => item.url).join('\n');

        textarea.value = currentUrls ? `${currentUrls}\n${newUrls}` : newUrls;

        // Show previews
        this.showImagePreviews(result.uploaded);

        // Show success message
        if (window.showToast) {
          window.showToast(
            'Upload Successful',
            `${result.uploaded.length} image(s) uploaded successfully`,
            'success',
            3000
          );
        }

        // Hide progress after delay
        setTimeout(() => {
          progressContainer.style.display = 'none';
        }, 2000);

      } else {
        throw new Error(result.error || 'Upload failed');
      }

    } catch (error) {
      console.error('Upload error:', error);

      // Hide progress and show error
      progressContainer.style.display = 'none';

      if (window.showToast) {
        window.showToast(
          'Upload Failed',
          error.message || 'Failed to upload images',
          'error',
          5000
        );
      }
    }
  }

  showImagePreviews(uploadedImages) {
    console.log('Showing image previews:', uploadedImages); // Debug log
    const previewsContainer = document.getElementById('image-previews');
    if (!previewsContainer) return;

    uploadedImages.forEach(item => {
      const preview = document.createElement('div');
      preview.className = 'image-preview';
      preview.innerHTML = `
        <img src="${item.url}" alt="Uploaded image" loading="lazy">
        <div class="preview-info">
          <span class="preview-filename">${item.filename}</span>
          <div class="preview-actions">
            <button type="button" class="btn-set-featured" data-url="${item.url}" title="Set as featured image" style="background: #ccc; border: none; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; cursor: pointer;">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12,17.27L18.18,21L16.54,13.97L22,9.24L14.81,8.62L12,2L9.19,8.62L2,9.24L7.46,13.97L5.82,21L12,17.27Z"/>
              </svg>
            </button>
            <button type="button" class="btn-remove-image" data-url="${item.url}" title="Remove image">×</button>
          </div>
        </div>
      `;

      // Add featured image functionality
      const featuredBtn = preview.querySelector('.btn-set-featured');
      featuredBtn.addEventListener('click', () => {
        this.setFeaturedImage(item.url);
      });

      // Add remove functionality
      const removeBtn = preview.querySelector('.btn-remove-image');
      removeBtn.addEventListener('click', () => {
        this.removeImageFromTextarea(item.url);
        preview.remove();
        // If this was the featured image, clear it
        if (this.featuredImageUrl === item.url) {
          this.setFeaturedImage(null);
        }
      });

      previewsContainer.appendChild(preview);
    });
  }

  setFeaturedImage(imageUrl) {
    console.log('Setting featured image:', imageUrl); // Debug log
    this.featuredImageUrl = imageUrl;

    // Update visual indicators
    const previews = document.querySelectorAll('.image-preview');
    previews.forEach(preview => {
      const img = preview.querySelector('img');
      const featuredBtn = preview.querySelector('.btn-set-featured');

      if (img && featuredBtn) {
        const isCurrentFeatured = img.src === imageUrl;

        // Update button state
        featuredBtn.classList.toggle('active', isCurrentFeatured);
        featuredBtn.title = isCurrentFeatured ? 'Featured image' : 'Set as featured image';

        // Update preview styling
        preview.classList.toggle('featured', isCurrentFeatured);
      }
    });

    // Show feedback
    if (window.showToast) {
      if (imageUrl) {
        window.showToast(
          'Featured Image Set',
          'This image will be displayed first in product listings',
          'success',
          2000
        );
      } else {
        window.showToast(
          'Featured Image Cleared',
          'The first image will be used as the featured image',
          'info',
          2000
        );
      }
    }
  }

  removeImageFromTextarea(urlToRemove) {
    const textarea = document.getElementById('product-images');
    const urls = textarea.value.split('\n').filter(url => url.trim() !== urlToRemove.trim());
    textarea.value = urls.join('\n');
  }

  // Custom styled confirmation dialog
  showConfirmDialog(title, message, details, confirmText = 'Confirm', type = 'danger') {
    return new Promise((resolve) => {
      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.className = 'confirm-modal-overlay';
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        backdrop-filter: blur(4px);
      `;

      // Create modal
      const modal = document.createElement('div');
      modal.className = 'confirm-modal';
      modal.style.cssText = `
        background: white;
        border-radius: 12px;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        transition: transform 0.2s ease;
      `;

      const typeColors = {
        danger: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
      };

      modal.innerHTML = `
        <div class="confirm-header" style="margin-bottom: 1rem;">
          <h3 style="margin: 0; color: #0f172a; font-size: 1.25rem; font-weight: 600;">${title}</h3>
        </div>
        <div class="confirm-body" style="margin-bottom: 2rem;">
          <p style="margin: 0 0 0.5rem 0; color: #374151; font-size: 1rem;">${message}</p>
          ${details ? `<p style="margin: 0; color: #6b7280; font-size: 0.875rem;">${details}</p>` : ''}
        </div>
        <div class="confirm-actions" style="display: flex; gap: 0.75rem; justify-content: flex-end;">
          <button class="confirm-cancel" style="
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            color: #374151;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s ease;
          ">Cancel</button>
          <button class="confirm-action" style="
            padding: 0.5rem 1rem;
            border: none;
            background: ${typeColors[type] || typeColors.danger};
            color: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s ease;
          ">${confirmText}</button>
        </div>
      `;

      overlay.appendChild(modal);
      document.body.appendChild(overlay);

      // Animate in
      requestAnimationFrame(() => {
        modal.style.transform = 'scale(1)';
      });

      // Event handlers
      const cleanup = () => {
        overlay.style.opacity = '0';
        modal.style.transform = 'scale(0.95)';
        setTimeout(() => {
          if (overlay.parentNode) {
            overlay.parentNode.removeChild(overlay);
          }
        }, 200);
      };

      modal.querySelector('.confirm-cancel').addEventListener('click', () => {
        cleanup();
        resolve(false);
      }, { passive: true });

      modal.querySelector('.confirm-action').addEventListener('click', () => {
        cleanup();
        resolve(true);
      });

      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          cleanup();
          resolve(false);
        }
      });

      // ESC key support
      const handleEsc = (e) => {
        if (e.key === 'Escape') {
          cleanup();
          resolve(false);
          document.removeEventListener('keydown', handleEsc);
        }
      };
      document.addEventListener('keydown', handleEsc);
    });
  }

  addKeyPoint() {
    const labelInput = document.getElementById('feature-label');
    const valueInput = document.getElementById('feature-value');

    if (!labelInput || !valueInput) return;

    const label = labelInput.value.trim();
    const value = valueInput.value.trim();

    if (label && value) {
      this.keyPoints.push({ label, value });
      this.renderKeyPoints();
      labelInput.value = '';
      valueInput.value = '';
      labelInput.focus();
    }
  }

  renderKeyPoints() {
    const container = document.getElementById('features-display');
    if (!container) return;

    container.innerHTML = this.keyPoints.map((point, index) => `
      <div class="keypoint-item">
        <span class="keypoint-label">${point.label}:</span>
        <span class="keypoint-value">${point.value}</span>
        <button type="button" class="btn-remove-keypoint" onclick="adminPanel.removeKeyPoint(${index})" title="Remove">×</button>
      </div>
    `).join('');
  }

  removeKeyPoint(index) {
    this.keyPoints.splice(index, 1);
    this.renderKeyPoints();
  }

  editProduct(productId) {
    const product = this.products.find(p => p.id === productId);
    if (!product) return;

    this.editingProduct = product;
    this.switchTab('form');
    this.populateForm(product);
    document.getElementById('form-title').textContent = 'Edit Product';
  }

  populateForm(product) {
    // Populate basic fields
    document.getElementById('product-name').value = product.name || '';
    document.getElementById('product-description').value = product.description || '';
    document.getElementById('product-category').value = product.category || '';
    document.getElementById('product-condition').value = product.condition || '';
    document.getElementById('product-price').value = product.price || '';
    document.getElementById('product-defects').value = product.defects || '';

    // Populate images
    if (product.images && product.images.length > 0) {
      document.getElementById('product-images').value = product.images.join('\n');

      // Set featured image
      this.featuredImageUrl = product.featuredImage || null;

      // Show image previews for existing images
      const existingImages = product.images.map((url, index) => ({
        url: url,
        filename: `existing-image-${index + 1}.jpg`
      }));
      this.showImagePreviews(existingImages);

      // Update featured image indicators after previews are shown
      if (this.featuredImageUrl) {
        setTimeout(() => this.setFeaturedImage(this.featuredImageUrl), 100);
      }
    }

    // Populate key points
    this.keyPoints = [...(product.keyPoints || [])];
    this.renderKeyPoints();
  }

  resetForm() {
    this.editingProduct = null;
    this.keyPoints = [];
    this.featuredImageUrl = null; // Clear featured image
    document.getElementById('form-title').textContent = 'Add New Product';

    // Reset all form fields
    const form = document.getElementById('product-form');
    if (form) {
      const inputs = form.querySelectorAll('input, textarea, select');
      inputs.forEach(input => {
        input.value = '';
      });
    }

    // Clear image previews
    const previewsContainer = document.getElementById('image-previews');
    if (previewsContainer) {
      previewsContainer.innerHTML = '';
    }

    this.renderKeyPoints();
  }

  saveProduct() {
    const form = document.getElementById('product-form');
    if (!form) return;

    // Get form data
    const formData = new FormData(form);
    const productData = {
      name: formData.get('name')?.trim(),
      description: formData.get('description')?.trim(),
      category: formData.get('category'),
      condition: formData.get('condition'),
      price: parseFloat(formData.get('price')) || 0,
      defects: formData.get('defects')?.trim() || '',
      images: formData.get('images')?.split('\n').filter(url => url.trim()).map(url => url.trim()) || [],
      featuredImage: this.featuredImageUrl || null,
      keyPoints: [...this.keyPoints],
      dateAdded: new Date().toISOString()
    };

    // Validate required fields
    if (!productData.name || !productData.description || !productData.category || !productData.condition || !productData.price) {
      if (window.showToast) {
        window.showToast(
          'Validation Error',
          'Please fill in all required fields: Name, Description, Category, Condition, and Price.',
          'error',
          4000
        );
      }
      return;
    }

    if (this.editingProduct) {
      // Update existing product
      productData.id = this.editingProduct.id;
      const index = this.products.findIndex(p => p.id === this.editingProduct.id);
      if (index !== -1) {
        this.products[index] = productData;
      }
    } else {
      // Add new product
      productData.id = 'product-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      this.products.push(productData);
    }

    this.hasUnsavedChanges = true;
    this.updateSyncButtonState();
    this.switchTab('list');
    this.resetForm();
    this.filterAndRenderProducts();

    // Show success message
    if (window.showToast) {
      window.showToast(
        this.editingProduct ? 'Product Updated' : 'Product Added',
        this.editingProduct ? 'Product updated successfully!' : 'Product added successfully!',
        'success',
        3000
      );
    }
  }

  async deleteProduct(productId) {
    // Create a custom confirmation dialog instead of using alert
    const product = this.products.find(p => p.id === productId);
    const productName = product ? product.name : 'this product';

    // Use custom styled confirmation dialog
    const confirmed = await this.showConfirmDialog(
      'Delete Product',
      `Are you sure you want to delete "${productName}"?`,
      'This action cannot be undone and will remove the product from your catalog.',
      'Delete Product',
      'danger'
    );

    if (!confirmed) return;

    const index = this.products.findIndex(p => p.id === productId);
    if (index !== -1) {
      this.products.splice(index, 1);
      this.hasUnsavedChanges = true;
      this.updateSyncButtonState();
      this.filterAndRenderProducts();

      // Show success toast
      if (window.showToast) {
        window.showToast(
          'Product Deleted',
          `"${productName}" has been deleted successfully.`,
          'warning',
          3000
        );
      }
    }
  }

  renderCategories() {
    const container = document.getElementById('categories-grid');
    if (!container) return;

    const categories = this.getCategories();

    if (categories.length === 0) {
      container.innerHTML = '<div class="no-categories">No categories found. Add your first category to get started.</div>';
      return;
    }

    // Create compact list layout
    container.innerHTML = `
      <div class="categories-list">
        <div class="categories-list-header">
          <div class="category-name-col">Category Name</div>
          <div class="category-count-col">Products</div>
          <div class="category-status-col">Status</div>
          <div class="category-actions-col">Actions</div>
        </div>
        <div class="categories-list-body">
          ${categories.map(category => {
            const productCount = this.products.filter(p => p.category === category).length;
            const isCreatedCategory = this.createdCategories.has(category) && productCount === 0;

            return `
              <div class="category-list-item ${isCreatedCategory ? 'category-unused' : ''}">
                <div class="category-name-col">
                  <div class="category-name">${category}</div>
                </div>
                <div class="category-count-col">
                  <span class="product-count">${productCount}</span>
                </div>
                <div class="category-status-col">
                  <span class="status-badge ${isCreatedCategory ? 'status-ready' : 'status-active'}">
                    ${isCreatedCategory ? 'Ready' : 'Active'}
                  </span>
                </div>
                <div class="category-actions-col">
                  <div class="category-actions">
                    <button class="btn-edit-category" onclick="adminPanel.editCategory('${category}')" title="Edit Category">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"/>
                      </svg>
                    </button>
                    <button class="btn-delete-category" onclick="adminPanel.deleteCategory('${category}')" title="Delete Category">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    `;
  }

  showCategoryForm() {
    const container = document.getElementById('category-form-container');
    const categoriesContainer = document.querySelector('.categories-container');
    if (container) {
      container.style.display = 'block';
      // Add class for browsers that don't support :has()
      if (categoriesContainer) {
        categoriesContainer.classList.add('form-visible');
      }
      document.getElementById('category-name').focus();
    }
  }

  hideCategoryForm() {
    const container = document.getElementById('category-form-container');
    const categoriesContainer = document.querySelector('.categories-container');
    if (container) {
      container.style.display = 'none';
      // Remove class for browsers that don't support :has()
      if (categoriesContainer) {
        categoriesContainer.classList.remove('form-visible');
      }
      document.getElementById('category-form').reset();
      this.editingCategory = null;
    }
  }

  saveCategory() {
    const name = document.getElementById('category-name').value.trim();
    const description = document.getElementById('category-description').value.trim();

    if (!name) {
      if (window.showToast) {
        window.showToast(
          'Validation Error',
          'Please enter a category name.',
          'error',
          4000
        );
      }
      return;
    }

    // Check if category already exists (case-insensitive)
    const existingCategories = this.getCategories();
    const categoryExists = existingCategories.some(cat =>
      cat.toLowerCase() === name.toLowerCase()
    );

    if (categoryExists && !this.editingCategory) {
      if (window.showToast) {
        window.showToast(
          'Category Exists',
          'A category with this name already exists.',
          'error',
          4000
        );
      }
      return;
    }

    // If editing, update products that use the old category name
    if (this.editingCategory && this.editingCategory !== name) {
      this.products.forEach(product => {
        if (product.category === this.editingCategory) {
          product.category = name;
        }
      });

      // Update created categories set
      this.createdCategories.delete(this.editingCategory);
      this.createdCategories.add(name);
    } else if (!this.editingCategory) {
      // Add new category to created categories set
      this.createdCategories.add(name);
    }

    this.hasUnsavedChanges = true;
    this.updateSyncButtonState();
    this.hideCategoryForm();
    this.renderCategories();
    this.renderStats();

    if (window.showToast) {
      window.showToast(
        this.editingCategory ? 'Category Updated' : 'Category Ready',
        this.editingCategory
          ? `Category "${name}" has been updated successfully. Category pages will be generated on next sync.`
          : `Category "${name}" is ready to use. Create products with this category to see it in the list. Category pages will be generated automatically.`,
        'success',
        4000
      );
    }
  }

  editCategory(categoryName) {
    this.editingCategory = categoryName;
    document.getElementById('category-name').value = categoryName;
    document.getElementById('category-form-title').textContent = 'Edit Category';
    this.showCategoryForm();
  }

  async deleteCategory(categoryName) {
    // Check how many products use this category
    const productsUsingCategory = this.products.filter(p => p.category === categoryName);

    // Use custom styled confirmation dialog
    const confirmed = await this.showConfirmDialog(
      'Delete Category',
      `Are you sure you want to delete the "${categoryName}" category?`,
      productsUsingCategory.length > 0
        ? `This will affect ${productsUsingCategory.length} product(s). Those products will need to be assigned to a different category.`
        : 'This action cannot be undone.',
      'Delete Category',
      'danger'
    );

    if (!confirmed) return;

    if (productsUsingCategory.length > 0) {
      // Show a dialog to choose what to do with products
      const action = await this.showCategoryDeletionDialog(categoryName, productsUsingCategory.length);

      if (action === 'cancel') return;

      if (action === 'uncategorized') {
        // Move products to "Uncategorized"
        productsUsingCategory.forEach(product => {
          product.category = 'Uncategorized';
        });
      } else if (action.startsWith('move-to-')) {
        // Move products to another category
        const targetCategory = action.replace('move-to-', '');
        productsUsingCategory.forEach(product => {
          product.category = targetCategory;
        });
      }
    }

    // Remove from created categories set
    this.createdCategories.delete(categoryName);

    this.hasUnsavedChanges = true;
    this.updateSyncButtonState();
    this.renderCategories();
    this.renderStats();
    this.filterAndRenderProducts();

    if (window.showToast) {
      window.showToast(
        'Category Deleted',
        `Category "${categoryName}" has been deleted successfully.`,
        'success',
        3000
      );
    }
  }

  // Category deletion dialog with options
  showCategoryDeletionDialog(categoryName, productCount) {
    return new Promise((resolve) => {
      const availableCategories = this.getCategories().filter(cat => cat !== categoryName);

      // Create modal overlay
      const overlay = document.createElement('div');
      overlay.className = 'confirm-modal-overlay';
      overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
      `;

      // Create modal
      const modal = document.createElement('div');
      modal.className = 'confirm-modal';
      modal.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 2rem;
        max-width: 500px;
        width: 90%;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      `;

      const categoryOptions = availableCategories.length > 0
        ? availableCategories.map(cat =>
            `<label style="display: block; margin: 0.5rem 0; cursor: pointer;">
              <input type="radio" name="category-action" value="move-to-${cat}" style="margin-right: 0.5rem;">
              Move to "${cat}"
            </label>`
          ).join('')
        : '';

      modal.innerHTML = `
        <h3 style="margin: 0 0 1rem 0; color: #dc2626;">What should happen to the ${productCount} product(s)?</h3>
        <div style="margin: 1rem 0;">
          <label style="display: block; margin: 0.5rem 0; cursor: pointer;">
            <input type="radio" name="category-action" value="uncategorized" style="margin-right: 0.5rem;" checked>
            Move to "Uncategorized"
          </label>
          ${categoryOptions}
        </div>
        <div style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
          <button id="cancel-deletion" style="padding: 0.5rem 1rem; border: 1px solid #d1d5db; background: white; border-radius: 4px; cursor: pointer;">Cancel</button>
          <button id="confirm-deletion" style="padding: 0.5rem 1rem; background: #dc2626; color: white; border: none; border-radius: 4px; cursor: pointer;">Delete Category</button>
        </div>
      `;

      overlay.appendChild(modal);
      document.body.appendChild(overlay);

      // Handle buttons
      const cancelBtn = modal.querySelector('#cancel-deletion');
      const confirmBtn = modal.querySelector('#confirm-deletion');

      cancelBtn.addEventListener('click', () => {
        document.body.removeChild(overlay);
        resolve('cancel');
      });

      confirmBtn.addEventListener('click', () => {
        const selectedAction = modal.querySelector('input[name="category-action"]:checked')?.value || 'uncategorized';
        document.body.removeChild(overlay);
        resolve(selectedAction);
      });

      // Close on overlay click
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          document.body.removeChild(overlay);
          resolve('cancel');
        }
      });
    });
  }

  updateSyncButtonState() {
    const syncBtn = document.getElementById('sync-products');
    if (!syncBtn) return;

    if (this.hasUnsavedChanges) {
      syncBtn.classList.add('has-changes');
      syncBtn.title = 'You have unsaved changes. Click to sync and deploy.';
    } else {
      syncBtn.classList.remove('has-changes');
      syncBtn.title = 'Sync with server and deploy';
    }
  }

  async checkGitHubStatus() {
    const indicator = document.getElementById('github-indicator');
    const text = document.getElementById('github-text');

    if (!indicator || !text) return;

    // Simulate GitHub status check
    indicator.textContent = '🟡';
    text.textContent = 'Checking...';

    setTimeout(() => {
      indicator.textContent = '🟢';
      text.textContent = 'Connected';
    }, 1000);
  }

  async testGitHub() {
    const indicator = document.getElementById('github-indicator');
    const text = document.getElementById('github-text');

    if (!indicator || !text) return;

    indicator.textContent = '🟡';
    text.textContent = 'Testing...';

    try {
      // Call the real GitHub test API
      const response = await fetch('/api/github-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'test-commit'
        })
      });

      const result = await response.json();
      console.log('GitHub test result:', result);

      if (result.success) {
        indicator.textContent = '🟢';
        text.textContent = 'Test successful';
        if (window.showToast) {
          window.showToast(
            'GitHub Test Successful',
            `Repository: ${result.repoInfo?.full_name || 'Connected'}\nCommit SHA: ${result.commitSha || 'N/A'}`,
            'success',
            5000
          );
        }
      } else {
        indicator.textContent = '🔴';
        text.textContent = 'Test failed';
        if (window.showToast) {
          window.showToast(
            'GitHub Test Failed',
            `${result.error || 'Unknown error'}\n\nPlease check your GitHub configuration.`,
            'error',
            6000
          );
        }
      }
    } catch (error) {
      console.error('GitHub test error:', error);
      indicator.textContent = '🔴';
      text.textContent = 'Test failed';
      if (window.showToast) {
        window.showToast(
          'GitHub Test Error',
          `${error.message}\n\nPlease check your internet connection and GitHub configuration.`,
          'error',
          6000
        );
      }
    }
  }

  async syncProducts() {
    const syncBtn = document.getElementById('sync-products');
    if (!syncBtn) return;

    const originalHTML = syncBtn.innerHTML;
    syncBtn.innerHTML = '<div class="spinner-small"></div> Syncing...';
    syncBtn.disabled = true;

    try {
      console.log('Starting sync process...');
      console.log('Products to sync:', this.products.length);
      console.log('Product IDs:', this.products.map(p => p.id));

      // Call the real sync API endpoint
      const response = await fetch('/api/sync-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          products: this.products,
          previousCategories: [...new Set(this.lastSyncedProducts.map(p => p.category))].filter(Boolean).sort(),
          createdCategories: Array.from(this.createdCategories)
        })
      });

      const result = await response.json();
      console.log('Sync API response:', result);

      if (result.success) {
        this.hasUnsavedChanges = false;
        this.lastSyncedProducts = [...this.products];
        this.lastSyncedCategories = [...new Set(this.products.map(p => p.category))].filter(Boolean).sort();
        this.updateSyncButtonState();

        if (window.showToast) {
          // Extract the correct values from the API response
          const productsCount = result.count || this.products.length;
          const githubCommitted = result.github?.committed || false;
          const githubCommitSha = result.github?.commitSha;

          // Check if cache was purged (we need to add this to the API response)
          const cachePurged = result.cache?.purged || false;

          let details = `• ${productsCount} products synced\n`;
          details += `• GitHub commit: ${githubCommitted ? 'Success' : 'Skipped'}`;
          if (githubCommitted && githubCommitSha) {
            details += ` (${githubCommitSha.substring(0, 7)})`;
          }
          details += `\n• Cache purged: ${cachePurged ? 'Yes' : 'No'}`;

          if (result.categoriesChanged) {
            details += `\n• Categories changed: Yes (${result.currentCategories?.length || 0} total)`;
            details += `\n• Build triggered: ${result.buildTriggered ? 'Yes (immediate)' : 'Auto (via GitHub)'}`;
          }

          window.showToast(
            'Sync Successful',
            `Products synced and deployed successfully!\n\n${details}`,
            'success',
            result.categoriesChanged ? 8000 : 6000
          );
        }
        this.renderProducts(); // Refresh to remove change indicators
      } else {
        console.error('Sync failed:', result.error);
        if (window.showToast) {
          window.showToast(
            'Sync Failed',
            `${result.error || 'Unknown error'}\n\nPlease check the console for more details and try again.`,
            'error',
            6000
          );
        }
      }
    } catch (error) {
      console.error('Sync error:', error);
      if (window.showToast) {
        window.showToast(
          'Sync Error',
          `${error.message}\n\nPlease check your internet connection and try again.`,
          'error',
          6000
        );
      }
    } finally {
      syncBtn.innerHTML = originalHTML;
      syncBtn.disabled = false;
    }
  }
}

// Export for use in other modules
window.ModernAdminPanel = ModernAdminPanel;
