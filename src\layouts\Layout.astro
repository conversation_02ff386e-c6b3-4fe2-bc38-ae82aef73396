---
import GoogleAnalytics from '../components/GoogleAnalytics.astro';
import ToastNotification from '../components/ToastNotification.astro';
import OrganizationSchema from '../components/seo/OrganizationSchema.astro';
// Import essential CSS for layout and styling
import '../assets/css/variables.css';
import '../assets/css/base.css';
import '../assets/css/header.css';
// Import critical CSS for above-the-fold content
import '../assets/css/critical.css';
// Import non-critical CSS (Astro will optimize loading)
import '../assets/css/non-critical.css';
// Import minimal Snipcart CSS (replaces full 16KB Snipcart CSS)
import '../assets/css/snipcart-minimal.css';

export interface Props {
	title: string;
	description?: string;
	image?: string;
	noIndex?: boolean;
}

const { title, description = "Cheers Marketplace - Quality secondhand goods in Chico, CA", image = "/cheers-marketplace-og.jpg", noIndex = false } = Astro.props;

// Generate canonical URL with trailing slash to match trailingSlash: 'always' config
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
// Ensure trailing slash for consistency with Astro config
if (!canonicalURL.pathname.endsWith('/') && !canonicalURL.pathname.includes('.')) {
  canonicalURL.pathname += '/';
}
---

<!doctype html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
	<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
	<meta name="generator" content={Astro.generator} />

	<!-- Performance optimizations for mobile -->
	<meta name="format-detection" content="telephone=no" />
	<meta name="mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="default" />

	<!-- Optimized font loading strategy - using system fonts for maximum performance -->
	<!-- System fonts provide instant loading with no external requests -->

	<!-- Preconnect to most critical domains (full connection setup) -->
	<link rel="preconnect" href="https://cdn.snipcart.com" crossorigin />
	<link rel="preconnect" href="https://www.googletagmanager.com" />
	<link rel="preconnect" href="https://images.unsplash.com" />

	<!-- Preload critical images for LCP optimization -->
	<!-- Note: This should be dynamically set based on the page content -->
	{Astro.url.pathname.startsWith('/products/') && (
		<link rel="preload" href="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=600&h=450&auto=format&fit=crop&q=90&fm=webp&dpr=1" as="image" fetchpriority="high" />
	)}

	<!-- System fonts only for maximum performance -->
	<!-- Google Fonts removed to eliminate third-party blocking -->

	<!-- Critical CSS inlined for instant rendering -->
	<style>
		/* Critical above-the-fold styles - optimized for performance */
		:root{--primary:#2563eb;--primary-dark:#1d4ed8;--primary-light:#3b82f6;--secondary:#1e293b;--background:#f8fafc;--light-background:#ffffff;--card-bg:#ffffff;--text:#0f172a;--text-secondary:#475569;--border:#e2e8f0;--radius:0.5rem;--radius-lg:0.75rem;--radius-xl:1rem;--container-width:1200px;--shadow:0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);--shadow-lg:0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);--font-system:system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,sans-serif;--font-serif:Georgia,"Times New Roman",serif}
		*,*::before,*::after{box-sizing:border-box}
		html,body{margin:0;padding:0;font-family:var(--font-system);background:var(--background);color:var(--text);line-height:1.6;font-size:16px;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}
		.container{max-width:var(--container-width);margin:0 auto;padding:0 2rem}
		h1,h2,h3{font-family:var(--font-serif);color:var(--text);font-weight:600;line-height:1.2;margin:0 0 1rem 0}
		h1{font-size:2.5rem}h2{font-size:2rem}h3{font-size:1.5rem}
		.site-header{background:var(--light-background);border-bottom:1px solid var(--border);position:sticky;top:0;z-index:100;backdrop-filter:blur(10px)}
		.header-flex{display:flex;align-items:center;justify-content:space-between;padding:1rem 2rem;min-height:70px}
		.logo{display:flex;align-items:center;gap:0.75rem;text-decoration:none;color:var(--text);font-weight:600;font-size:1.25rem}
		.main-nav{display:flex;gap:2rem;align-items:center}
		.main-nav a{text-decoration:none;color:var(--text-secondary);font-weight:500;transition:color 0.2s ease}
		.main-nav a:hover{color:var(--primary)}
		.btn{display:inline-flex;align-items:center;justify-content:center;padding:0.75rem 1.5rem;border:none;border-radius:var(--radius);font-weight:500;text-decoration:none;transition:all 0.2s ease;cursor:pointer;font-size:0.875rem}
		.btn.primary{background:var(--primary);color:white}
		.btn.primary:hover{background:var(--primary-dark)}
		.cart-trigger{position:relative;background:none;border:none;padding:0.5rem;cursor:pointer;color:var(--text-secondary);transition:color 0.2s ease}
		.cart-trigger:hover{color:var(--primary)}
		.cart-count{position:absolute;top:-5px;right:-5px;background:var(--primary);color:white;border-radius:50%;width:20px;height:20px;font-size:0.75rem;display:flex;align-items:center;justify-content:center;font-weight:600}
		.mobile-menu-toggle{display:none;flex-direction:column;gap:4px;background:none;border:none;padding:0.5rem;cursor:pointer}
		.hamburger-line{width:24px;height:2px;background:var(--text);transition:all 0.3s ease}
		@media (max-width: 768px){.main-nav{display:none}.mobile-menu-toggle{display:flex}.container{padding:0 1rem}.header-flex{padding:1rem}h1{font-size:2rem}h2{font-size:1.75rem}h3{font-size:1.25rem}}
		.loading{opacity:0.6;pointer-events:none}
		.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}
		*:focus-visible{outline:2px solid var(--primary);outline-offset:2px}
	</style>

	<!-- CSS optimization complete - page-specific CSS is already loaded by Astro -->



	<!-- Dynamic meta tags -->
	<title>{title}</title>
	<meta name="description" content={description} />
	<meta name="keywords" content="cheap used goods, affordable secondhand, quality thrift, budget items, Chico CA, family business, pre-owned, discount goods, bargain finds, low cost, inexpensive, economical, value shopping" />
	<meta name="author" content="Cheers Marketplace" />
	<meta name="robots" content={noIndex ? "noindex, nofollow" : "index, follow"} />
	<link rel="canonical" href={canonicalURL} />

	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={canonicalURL} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={new URL(image, Astro.site)} />

	<!-- Twitter -->
	<meta name="twitter:card" content="summary_large_image" />
	<meta name="twitter:url" content={canonicalURL} />
	<meta name="twitter:title" content={title} />
	<meta name="twitter:description" content={description} />
	<meta name="twitter:image" content={new URL(image, Astro.site)} />

	<!-- Google Analytics -->
	<GoogleAnalytics measurementId="G-5FH6Y2MPJN" />

	<!-- Organization Structured Data -->
	<OrganizationSchema />



	<!-- Page-specific head content -->
	<slot name="head" />
</head>
	<body>
		<slot />

		<!-- Toast Notifications -->
		<ToastNotification />

		<!-- Optimized Snipcart v3 Integration - Load only when needed -->
		<script define:vars={{ snipcartPublicKey: (import.meta as any).env?.PUBLIC_SNIPCART_API_KEY || 'YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4' }} is:inline>
			// Optimized Snipcart loading - only load when cart functionality is actually needed
			window.SnipcartSettings = {
				publicApiKey: snipcartPublicKey,
				loadStrategy: "manual", // Changed from "on-user-interaction" to "manual"
				currency: "usd",
				addProductBehavior: "none",
				modalStyle: "side",
				loadCSS: false // We'll load CSS manually to reduce unused styles
			};

			// Track if Snipcart is loaded to avoid duplicate loading
			let snipcartLoaded = false;
			let snipcartLoading = false;

			// Function to load Snipcart when actually needed
			function loadSnipcart() {
				if (snipcartLoaded || snipcartLoading) return Promise.resolve();

				snipcartLoading = true;

				return new Promise((resolve, reject) => {
					// Create Snipcart container
					let snipcartDiv = document.querySelector("#snipcart");
					if (!snipcartDiv) {
						snipcartDiv = document.createElement("div");
						snipcartDiv.id = "snipcart";
						snipcartDiv.setAttribute("hidden", "true");
						document.body.appendChild(snipcartDiv);
					}

					// Configure Snipcart div
					snipcartDiv.dataset.apiKey = window.SnipcartSettings.publicApiKey;
					if (window.SnipcartSettings.addProductBehavior) {
						snipcartDiv.dataset.configAddProductBehavior = window.SnipcartSettings.addProductBehavior;
					}
					if (window.SnipcartSettings.modalStyle) {
						snipcartDiv.dataset.configModalStyle = window.SnipcartSettings.modalStyle;
					}
					if (window.SnipcartSettings.currency) {
						snipcartDiv.dataset.currency = window.SnipcartSettings.currency;
					}

					// Skip loading Snipcart CSS - we use our own minimal version
					// This saves 16KB of unused CSS

					// Load Snipcart JavaScript
					const script = document.createElement("script");
					script.src = "https://cdn.snipcart.com/themes/v3.4.1/default/snipcart.js";
					script.async = true;

					script.onload = () => {
						snipcartLoaded = true;
						snipcartLoading = false;
						resolve();
					};

					script.onerror = () => {
						snipcartLoading = false;
						reject(new Error('Failed to load Snipcart'));
					};

					document.head.appendChild(script);
				});
			}

			// Expose global function for cart loading
			window.loadSnipcart = loadSnipcart;

			// Add event listeners for cart-related actions
			document.addEventListener('DOMContentLoaded', function() {
				// Load Snipcart when cart button is clicked
				document.addEventListener('click', function(e) {
					const target = e.target.closest('.snipcart-checkout, .snipcart-add-item, .snipcart-customer-signin');
					if (target) {
						e.preventDefault();
						loadSnipcart().then(() => {
							// Re-trigger the click after Snipcart is loaded
							target.click();
						}).catch(console.error);
					}
				});

				// Update cart count display (lightweight implementation)
				function updateCartCount() {
					const cartCountElements = document.querySelectorAll('.snipcart-items-count');
					const count = localStorage.getItem('snipcart-cart-count') || '0';
					cartCountElements.forEach(el => {
						el.textContent = count;
						el.style.display = count === '0' ? 'none' : 'inline';
					});
				}

				// Initialize cart count
				updateCartCount();

				// Listen for storage changes to update cart count across tabs
				window.addEventListener('storage', function(e) {
					if (e.key === 'snipcart-cart-count') {
						updateCartCount();
					}
				});
			});

			// Load performance monitoring in development/testing
			if (window.location.hostname === 'localhost' || window.location.search.includes('debug=performance')) {
				import('/src/assets/js/performance-monitor.js').catch(console.error);
			}
		</script>

		<!-- Snipcart Custom Styling -->
		<style is:global>
			/* Ensure Snipcart modal appears above everything including toasts */
			.snipcart-modal__container {
				z-index: 10001 !important;
			}

			.snipcart__modal {
				z-index: 10001 !important;
			}

			/* Match Cheers Marketplace branding */
			.snipcart-layout {
				font-family: system-ui, -apple-system, sans-serif !important;
			}

			.snipcart__box--badge-highlight {
				background-color: #2563eb !important;
			}

			.snipcart__button--primary {
				background-color: #2563eb !important;
				border-color: #2563eb !important;
			}

			.snipcart__button--primary:hover {
				background-color: #1d4ed8 !important;
				border-color: #1d4ed8 !important;
			}

			/* Side modal specific styling */
			.snipcart__modal--side {
				font-family: system-ui, -apple-system, sans-serif !important;
			}
		</style>
	</body>
</html>


