#!/usr/bin/env python3
"""
HAR File Performance Analysis Tool
Analyzes HAR (HTTP Archive) files to identify performance optimization opportunities
"""

import json
import sys
from datetime import datetime
from urllib.parse import urlparse

def analyze_har_file(har_file_path):
    """Analyze HAR file and provide performance insights"""
    
    try:
        with open(har_file_path, 'r') as f:
            har_data = json.load(f)
    except Exception as e:
        print(f"Error reading HAR file: {e}")
        return
    
    entries = har_data['log']['entries']
    page_timings = har_data['log']['pages'][0]['pageTimings']
    
    print("🔍 HAR Performance Analysis Report")
    print("=" * 50)
    
    # Page Load Metrics
    print("\n📊 Page Load Metrics:")
    print(f"   • DOMContentLoaded: {page_timings['onContentLoad']}ms")
    print(f"   • Load Event: {page_timings['onLoad']}ms")
    print(f"   • Fully Loaded: {page_timings.get('_fullyLoaded', 'N/A')}ms")
    
    # Resource Analysis
    total_size = 0
    total_time = 0
    resource_types = {}
    slow_resources = []
    large_resources = []
    
    print(f"\n📦 Resource Analysis ({len(entries)} resources):")
    
    for entry in entries:
        url = entry['request']['url']
        domain = urlparse(url).netloc
        size = entry['response']['content']['size']
        time = entry['time']
        resource_type = entry['response']['content'].get('_resourceType', 'Unknown')
        status = entry['response']['status']
        
        total_size += size
        total_time += time
        
        # Track resource types
        if resource_type not in resource_types:
            resource_types[resource_type] = {'count': 0, 'size': 0, 'time': 0}
        resource_types[resource_type]['count'] += 1
        resource_types[resource_type]['size'] += size
        resource_types[resource_type]['time'] += time
        
        # Identify slow resources (>100ms)
        if time > 100:
            slow_resources.append({
                'url': url,
                'time': time,
                'size': size,
                'type': resource_type
            })
        
        # Identify large resources (>50KB)
        if size > 50000:
            large_resources.append({
                'url': url,
                'size': size,
                'time': time,
                'type': resource_type
            })
    
    print(f"   • Total Size: {total_size / 1024:.1f} KB")
    print(f"   • Total Time: {total_time:.1f}ms")
    
    # Resource Type Breakdown
    print(f"\n📋 Resource Type Breakdown:")
    for res_type, data in sorted(resource_types.items(), key=lambda x: x[1]['size'], reverse=True):
        print(f"   • {res_type}: {data['count']} files, {data['size'] / 1024:.1f} KB, {data['time']:.1f}ms")
    
    # Slow Resources
    if slow_resources:
        print(f"\n⚠️  Slow Resources (>100ms):")
        for resource in sorted(slow_resources, key=lambda x: x['time'], reverse=True)[:5]:
            print(f"   • {resource['time']:.1f}ms - {resource['type']} - {urlparse(resource['url']).path}")
    
    # Large Resources
    if large_resources:
        print(f"\n📦 Large Resources (>50KB):")
        for resource in sorted(large_resources, key=lambda x: x['size'], reverse=True)[:5]:
            print(f"   • {resource['size'] / 1024:.1f} KB - {resource['type']} - {urlparse(resource['url']).path}")
    
    # Cache Analysis
    print(f"\n💾 Cache Analysis:")
    cache_hits = 0
    cache_misses = 0
    
    for entry in entries:
        cache_status = None
        for header in entry['response']['headers']:
            if header['name'].lower() == 'cf-cache-status':
                cache_status = header['value']
                break
        
        if cache_status == 'HIT':
            cache_hits += 1
        elif cache_status == 'MISS':
            cache_misses += 1
    
    print(f"   • Cache Hits: {cache_hits}")
    print(f"   • Cache Misses: {cache_misses}")
    if cache_hits + cache_misses > 0:
        hit_rate = (cache_hits / (cache_hits + cache_misses)) * 100
        print(f"   • Hit Rate: {hit_rate:.1f}%")
    
    # Performance Recommendations
    print(f"\n🚀 Performance Recommendations:")
    
    # Check for render-blocking resources
    render_blocking = []
    for entry in entries:
        if entry['response']['content'].get('_resourceType') in ['Stylesheet', 'Script']:
            if entry['time'] > 50:  # Resources taking >50ms
                render_blocking.append(entry)
    
    if render_blocking:
        print(f"   • Optimize {len(render_blocking)} render-blocking resources")
    
    # Check for unoptimized images
    unoptimized_images = [r for r in large_resources if r['type'] == 'Image']
    if unoptimized_images:
        print(f"   • Optimize {len(unoptimized_images)} large images")
    
    # Check for too many requests
    if len(entries) > 20:
        print(f"   • Consider reducing HTTP requests ({len(entries)} total)")
    
    # Check for slow third-party resources
    third_party_slow = []
    for resource in slow_resources:
        domain = urlparse(resource['url']).netloc
        if 'cheersmarketplace.com' not in domain:
            third_party_slow.append(resource)
    
    if third_party_slow:
        print(f"   • Optimize {len(third_party_slow)} slow third-party resources")
    
    print(f"\n✅ Analysis Complete!")

if __name__ == "__main__":
    analyze_har_file("har.json")
