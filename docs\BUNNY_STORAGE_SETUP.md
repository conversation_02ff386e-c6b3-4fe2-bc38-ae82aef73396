# Bunny Storage Integration Setup

This guide will help you set up the Bunny Storage API integration for automated image uploads in your admin panel.

## Prerequisites

1. A Bunny CDN account with a Storage Zone configured
2. Your Storage Zone credentials

## Setup Steps

### 1. Get Your Bunny Storage Credentials

1. Log into your [Bunny CDN Dashboard](https://panel.bunny.net/)
2. Go to **Storage** → **Storage Zones**
3. Select your storage zone or create a new one
4. Note down the following information:
   - **Storage Zone Name**: The name of your storage zone
   - **Storage Zone Password**: Found in the "FTP & API Access" section
   - **Region**: The primary region of your storage zone (e.g., 'de', 'ny', 'la', etc.)
   - **CDN URL**: Your pull zone URL (e.g., `https://your-zone.b-cdn.net`)

### 2. Configure Environment Variables

Create a `.env` file in your project root (if you don't have one) and add the following variables:

```env
# Bunny Storage Configuration
BUNNY_STORAGE_ZONE_NAME=your_storage_zone_name
BUNNY_STORAGE_API_KEY=your_storage_zone_password
BUNNY_STORAGE_REGION=your_storage_region
BUNNY_STORAGE_BASE_URL=https://your-cdn-url.b-cdn.net
```

**Important Notes:**
- The `BUNNY_STORAGE_API_KEY` is your Storage Zone Password, not your global Bunny API key
- The `BUNNY_STORAGE_REGION` should be the region code (e.g., 'de' for Frankfurt, 'ny' for New York)
- The `BUNNY_STORAGE_BASE_URL` should be your CDN pull zone URL

### 3. Region Codes Reference

| Region | Code | Endpoint |
|--------|------|----------|
| Frankfurt, DE | `de` | storage.bunnycdn.com |
| London, UK | `uk` | uk.storage.bunnycdn.com |
| New York, US | `ny` | ny.storage.bunnycdn.com |
| Los Angeles, US | `la` | la.storage.bunnycdn.com |
| Singapore, SG | `sg` | sg.storage.bunnycdn.com |
| Stockholm, SE | `se` | se.storage.bunnycdn.com |
| São Paulo, BR | `br` | br.storage.bunnycdn.com |
| Johannesburg, SA | `jh` | jh.storage.bunnycdn.com |
| Sydney, SYD | `syd` | syd.storage.bunnycdn.com |

### 4. Deploy and Test

1. Deploy your changes to Cloudflare Pages
2. Access your admin panel
3. Try uploading an image using the new upload area
4. Verify that the image appears in your Bunny Storage zone
5. Check that the CDN URL is automatically added to the image URLs textarea

## Features

### Image Upload
- **Drag and Drop**: Drag images directly onto the upload area
- **Click to Select**: Click the upload area to open file browser
- **Multiple Files**: Upload multiple images at once
- **Progress Indicator**: Visual feedback during upload
- **Image Previews**: See thumbnails of uploaded images
- **Automatic URL Population**: CDN URLs are automatically added to the textarea

### File Validation
- **File Types**: Only JPEG, PNG, and WebP images are allowed
- **File Size**: Maximum 10MB per image
- **Error Handling**: Clear error messages for validation failures

### Fallback Options
- **Manual URL Entry**: You can still manually enter image URLs
- **Mixed Usage**: Combine uploaded images with manual URLs
- **Remove Images**: Click the × button to remove uploaded images

## Troubleshooting

### Common Issues

1. **"Missing required Bunny Storage configuration" Error**
   - Check that all environment variables are set correctly
   - Ensure the `.env` file is in the project root
   - Redeploy after adding environment variables

2. **"Upload failed: 401 Unauthorized" Error**
   - Verify your Storage Zone Password (API Key) is correct
   - Check that you're using the Storage Zone Password, not the global API key

3. **"Upload failed: 404 Not Found" Error**
   - Verify your Storage Zone Name is correct
   - Check that the region code matches your storage zone's region

4. **Images upload but don't display**
   - Verify your CDN URL (BUNNY_STORAGE_BASE_URL) is correct
   - Check that your pull zone is properly configured
   - Ensure the pull zone is connected to your storage zone

### Testing the Setup

You can test your configuration by:

1. Checking the browser console for any JavaScript errors
2. Using the browser's Network tab to see API requests
3. Verifying files appear in your Bunny Storage zone dashboard
4. Testing the generated CDN URLs in a browser

## Security Notes

- Storage credentials are handled server-side only
- File uploads are validated for type and size
- Unique filenames prevent conflicts
- Files are organized in a `/products/` folder structure

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your Bunny Storage zone configuration
3. Test your credentials using Bunny's API documentation
4. Ensure your environment variables are properly set in Cloudflare Pages
