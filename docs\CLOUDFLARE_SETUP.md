# Cloudflare Pages Build Hook Setup

This guide explains how to set up automatic deployments when products are updated through the admin panel.

## 1. Get Your Cloudflare Pages Build Hook URL

1. Go to your Cloudflare Pages dashboard
2. Select your project
3. Go to **Settings** > **Builds & deployments**
4. Scroll down to **Build hooks**
5. Click **Create build hook**
6. Give it a name like "Admin Panel Product Updates"
7. Copy the generated webhook URL

## 2. Configure Environment Variables

### For Development (Local)
Create a `.env` file in your project root:

```bash
CLOUDFLARE_BUILD_HOOK_URL=https://api.cloudflare.com/client/v4/pages/webhooks/deploy_hooks/aa9b99b4-1be1-4fe2-bff4-cb4610646e5c
```

### For Production (Cloudflare Pages)
1. Go to your Cloudflare Pages project settings
2. Navigate to **Environment variables**
3. Add the following variables:

**GitHub Integration (Required for automatic deployment):**
   - **Variable name**: `GITHUB_OWNER` | **Value**: your-github-username
   - **Variable name**: `GITHUB_REPO` | **Value**: your-repository-name
   - **Variable name**: `GITHUB_TOKEN` | **Value**: your-github-personal-access-token
   - **Variable name**: `GITHUB_BRANCH` | **Value**: main (optional)

**Build Hook (Optional fallback):**
   - **Variable name**: `CLOUDFLARE_BUILD_HOOK_URL`
   - **Value**: Your webhook URL from step 1
   - **Environment**: Production (and Preview if needed)

> **Important**: For automatic deployment from the admin panel, you must set up GitHub integration. See [GITHUB_INTEGRATION_SETUP.md](./GITHUB_INTEGRATION_SETUP.md) for detailed setup instructions.

## 3. How It Works

### With GitHub Integration (Recommended)
When you make changes in the admin panel:

1. **Product Added/Updated/Deleted** → Saved to localStorage
2. **Sync & Deploy** → Data sent to `/api/sync-products`
3. **Update Local File** → `src/data/products.json` is updated locally
4. **Commit to GitHub** → Changes automatically committed to your repository
5. **Auto Deploy** → Cloudflare Pages detects GitHub changes and rebuilds
6. **Live Update** → Site updates with new product data

### Fallback Method (Without GitHub)
If GitHub integration isn't configured:

1. **Product Added/Updated/Deleted** → Saved to localStorage
2. **Sync to Server** → Data sent to `/api/sync-products`
3. **Update JSON File** → `src/data/products.json` is updated locally
4. **Manual Deploy** → You must manually commit and push changes to trigger deployment

## 4. Admin Panel Access

The admin panel is accessible at `/admin?admin=1`

**Security Note**: In production, you should implement proper authentication. The current setup uses a URL parameter for simplicity.

## 5. Manual Sync

If you need to manually sync products or trigger a build:

### Sync Products
```bash
curl -X POST https://your-site.pages.dev/api/sync-products \
  -H "Content-Type: application/json" \
  -d '{"products": [...]}'
```

### Trigger Build
```bash
curl -X POST https://your-site.pages.dev/api/build-hook \
  -H "Content-Type: application/json" \
  -d '{"trigger": "manual"}'
```

## 6. Monitoring

- Check the **Functions** tab in Cloudflare Pages for API logs
- Monitor build status in the **Deployments** tab
- Use browser dev tools to debug admin panel issues

## 7. Troubleshooting

### Build Hook Not Working
1. Verify the `CLOUDFLARE_BUILD_HOOK_URL` environment variable is set
2. Check the webhook URL is correct
3. Look for errors in the Functions logs

### Products Not Syncing
1. Check browser console for JavaScript errors
2. Verify the `/api/sync-products` endpoint is working
3. Ensure localStorage has the latest data

### Admin Panel Not Loading
1. Make sure you're accessing `/admin?admin=1`
2. Check for JavaScript errors in browser console
3. Verify the products data is loading correctly

## 8. Performance Optimization

The system is optimized for Cloudflare Pages:

- **Static Generation**: Products are loaded at build time
- **API Routes**: Handle dynamic admin functionality
- **Minimal JavaScript**: Core site works without JS
- **CDN Caching**: Automatic caching through Cloudflare

## 9. Bunny CDN Integration

Since you're using Bunny CDN for images:

1. Upload images to your Bunny CDN storage
2. Use the CDN URLs in the admin panel image fields
3. Images will be automatically optimized and cached

Example image URL format:
```
https://your-zone.b-cdn.net/products/image-name.jpg
```

## 10. Security Considerations

For production use, consider:

1. **Authentication**: Implement proper admin authentication
2. **Rate Limiting**: Add rate limits to API endpoints
3. **Input Validation**: Validate all product data
4. **HTTPS**: Ensure all requests use HTTPS
5. **Access Control**: Restrict admin panel access by IP or other means
