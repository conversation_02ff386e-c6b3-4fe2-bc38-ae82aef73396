---
// Optimized Sni<PERSON>cart cart button with loading state
---

<!-- Cart trigger button for header -->
<button
  class="snipcart-checkout cart-trigger"
  type="button"
  aria-label="Open shopping cart"
  id="cart-button"
>
  <svg class="cart-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <circle cx="9" cy="21" r="1"></circle>
    <circle cx="20" cy="21" r="1"></circle>
    <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
  </svg>
  <!-- Loading spinner (hidden by default) -->
  <svg class="loading-spinner" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
    <path d="M21 12a9 9 0 11-6.219-8.56"/>
  </svg>
  <span class="cart-count snipcart-items-count"></span>
</button>

<style>
  .cart-trigger {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    color: var(--text);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius);
    transition: all 0.2s ease;
  }

  .cart-trigger:hover {
    background-color: var(--background);
    color: var(--primary);
  }

  .cart-trigger:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  .cart-count {
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(25%, -25%);
  }

  .cart-count:not(:empty) {
    display: flex;
  }

  .loading-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .cart-trigger.loading .cart-icon {
    display: none;
  }

  .cart-trigger.loading .loading-spinner {
    display: block !important;
  }
</style>

<script>
  // Add loading state management for cart button
  document.addEventListener('DOMContentLoaded', function() {
    const cartButton = document.getElementById('cart-button');

    if (cartButton) {
      cartButton.addEventListener('click', function() {
        // Add loading state
        this.classList.add('loading');
        this.setAttribute('aria-label', 'Loading cart...');

        // Remove loading state after a short delay (Snipcart should be loaded by then)
        setTimeout(() => {
          this.classList.remove('loading');
          this.setAttribute('aria-label', 'Open shopping cart');
        }, 1500);
      });
    }
  });
</script>

<style>
  .cart-trigger {
    position: relative;
    background: none;
    border: none;
    color: var(--text-primary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .cart-trigger:hover {
    background-color: var(--bg-secondary);
    color: var(--primary);
  }

  .cart-trigger:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
  }

  .cart-count {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: var(--primary);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    line-height: 1;
  }

  .cart-count:empty {
    display: none;
  }

  /* Mobile adjustments */
  @media (max-width: 768px) {
    .cart-trigger {
      padding: 0.75rem;
    }
  }
</style>
