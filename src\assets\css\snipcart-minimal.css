/* Minimal Snipcart CSS - Only Essential Styles */
/* This replaces the full 16KB Snipcart CSS with only what we actually use */

/* Snipcart Modal Base Styles */
.snipcart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}

.snipcart-modal__container {
  background: white;
  border-radius: 8px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 10001;
}

/* Side Modal Styles */
.snipcart__modal--side {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 15px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 10001;
}

.snipcart__modal--side.snipcart__modal--opened {
  transform: translateX(0);
}

/* Cart Content Styles */
.snipcart-layout {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  padding: 1.5rem;
}

.snipcart__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 1rem;
}

.snipcart__header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

/* Cart Items */
.snipcart-item {
  display: flex;
  gap: 1rem;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.snipcart-item__image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
}

.snipcart-item__content {
  flex: 1;
}

.snipcart-item__name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.snipcart-item__price {
  color: #2563eb;
  font-weight: 600;
}

/* Buttons */
.snipcart__button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.snipcart__button--primary {
  background-color: #2563eb;
  color: white;
  border: 2px solid #2563eb;
}

.snipcart__button--primary:hover {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
}

.snipcart__button--secondary {
  background-color: transparent;
  color: #2563eb;
  border: 2px solid #2563eb;
}

.snipcart__button--secondary:hover {
  background-color: #2563eb;
  color: white;
}

/* Cart Summary */
.snipcart__summary {
  padding: 1rem 0;
  border-top: 2px solid #e5e7eb;
  margin-top: 1rem;
}

.snipcart__summary-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.snipcart__summary-total {
  font-weight: 700;
  font-size: 1.125rem;
  color: #111827;
}

/* Close Button */
.snipcart__close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6b7280;
  padding: 0.25rem;
  border-radius: 4px;
}

.snipcart__close:hover {
  color: #111827;
  background-color: #f3f4f6;
}

/* Quantity Controls */
.snipcart-quantity-trigger {
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  color: #374151;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
}

.snipcart-quantity-trigger:hover {
  background: #e5e7eb;
}

.snipcart__quantity {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.snipcart__quantity input {
  width: 60px;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  padding: 0.25rem;
}

/* Form Elements */
.snipcart__form input,
.snipcart__form select,
.snipcart__form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
}

.snipcart__form input:focus,
.snipcart__form select:focus,
.snipcart__form textarea:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Loading States */
.snipcart__loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.snipcart__spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .snipcart__modal--side {
    width: 100vw;
    right: 0;
  }
  
  .snipcart-modal__container {
    max-width: 95vw;
    margin: 1rem;
  }
  
  .snipcart-layout {
    padding: 1rem;
  }
}

/* Hide elements that are not needed */
.snipcart__badge,
.snipcart__notification,
.snipcart__powered-by {
  display: none !important;
}

/* Ensure proper z-index stacking */
.snipcart-modal,
.snipcart__modal,
.snipcart-modal__container {
  z-index: 10001 !important;
}
