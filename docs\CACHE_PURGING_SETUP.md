# Cloudflare Cache Purging Setup

This guide explains how to set up automatic cache purging after deployments to ensure users always see the latest content when products are updated.

## Overview

The cache purging system works in two ways:

1. **Immediate Purging**: After successful GitHub commits from the admin panel
2. **Post-Deployment Purging**: After Cloudflare Pages completes deployment (via webhook)

## Required Environment Variables

Add these environment variables to your Cloudflare Pages project:

### 1. Cloudflare API Credentials
```bash
CLOUDFLARE_ZONE_ID=your_zone_id_here
CLOUDFLARE_API_TOKEN=your_api_token_here
SITE_URL=https://www.cheersmarketplace.com
```

### 2. Existing GitHub Integration (Required)
```bash
GITHUB_OWNER=your-github-username
GITHUB_REPO=your-repository-name
GITHUB_TOKEN=your_github_token
```

## Step-by-Step Setup

### Step 1: Get Your Cloudflare Zone ID

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com)
2. Select your domain (cheersmarketplace.com)
3. In the right sidebar, copy the **Zone ID**

### Step 2: Create a Cloudflare API Token

1. Go to [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. Click **Create Token**
3. Use **Custom token** template
4. Configure the token:
   - **Token name**: `Cheers Marketplace Cache Purging`
   - **Permissions**:
     - Zone:Zone:Read
     - Zone:Cache Purge:Edit
   - **Zone Resources**:
     - Include: Specific zone → cheersmarketplace.com
   - **Client IP Address Filtering**: Leave empty (optional)
   - **TTL**: Leave empty (no expiration)

5. Click **Continue to summary**
6. Click **Create Token**
7. **Copy the token** (you won't see it again!)

### Step 3: Add Environment Variables to Cloudflare Pages

1. Go to your Cloudflare Pages dashboard
2. Select your project
3. Go to **Settings** → **Environment variables**
4. Add these variables for **Production**:

```
CLOUDFLARE_ZONE_ID = your_zone_id_from_step_1
CLOUDFLARE_API_TOKEN = your_api_token_from_step_2
SITE_URL = https://www.cheersmarketplace.com
```

### Step 4: Set Up Deployment Webhook (Optional)

For post-deployment cache purging, you can set up a deployment webhook:

1. In Cloudflare Pages, go to **Settings** → **Functions**
2. Add a **Deploy Hook** (if available) pointing to:
   ```
   https://www.cheersmarketplace.com/api/deployment-webhook
   ```

**Note**: This step is optional as the immediate cache purging after GitHub commits is usually sufficient.

## How It Works

### Immediate Cache Purging (Primary Method)

1. **Admin Panel Change** → Product added/updated/deleted
2. **Sync & Deploy** → Changes committed to GitHub
3. **Immediate Cache Purge** → Critical URLs purged instantly
4. **Cloudflare Pages Build** → Site rebuilds automatically
5. **Fresh Content** → Users see updated content immediately

### URLs That Get Purged

When products change, these URLs are automatically purged:

**Critical Pages:**
- `/` (homepage)
- `/products` (product listing)
- `/api/products.json` (API endpoint)
- `/sitemap.xml` (sitemap)

**Category Pages:**
- `/products/electronics`
- `/products/clothing`
- `/products/books`
- `/products/home-decor`
- `/products/arts-crafts`

**Individual Product Pages:**
- `/products/[product-slug]` (for each changed product)

## Testing the Setup

### Test Cache Configuration

Visit: `https://www.cheersmarketplace.com/api/deployment-webhook`

This will show:
- Whether cache purging is configured
- Which environment variables are set
- Configuration status

### Test Manual Cache Purge

You can test cache purging manually:

```bash
curl -X POST https://www.cheersmarketplace.com/api/deployment-webhook \
  -H "Content-Type: application/json" \
  -d '{
    "id": "test-deployment",
    "environment": "production",
    "latest_stage": {"status": "success"},
    "project_name": "cheersmarketplace",
    "url": "https://www.cheersmarketplace.com"
  }'
```

### Verify Cache Purging in Admin Panel

1. Go to `/admin?admin=1`
2. Make a product change
3. Click **Sync & Deploy**
4. Check browser console for cache purging logs
5. Look for messages like "✅ Cache purged successfully"

## Monitoring and Troubleshooting

### Check Cache Purging Status

The admin panel will show cache purging status in the sync response:

```javascript
{
  "success": true,
  "message": "Successfully processed X products and committed to GitHub",
  "github": {
    "committed": true,
    "commitSha": "abc123..."
  },
  "cache_purge": {
    "success": true,
    "urls_purged": 15
  }
}
```

### Common Issues

**Cache purging not working:**
1. Verify `CLOUDFLARE_ZONE_ID` and `CLOUDFLARE_API_TOKEN` are set
2. Check API token permissions include Cache Purge:Edit
3. Ensure token is for the correct zone
4. Check Cloudflare Pages function logs for errors

**Partial cache purging:**
1. Some URLs might be cached by browsers - try hard refresh (Ctrl+F5)
2. CDN edge locations may take a few minutes to update globally
3. Check if custom caching rules are interfering

**API token issues:**
1. Regenerate the API token if it's not working
2. Ensure token hasn't expired
3. Verify token permissions are correct

### Performance Impact

✅ **Zero Performance Impact**
- Cache purging happens asynchronously after GitHub commits
- No impact on admin panel responsiveness
- No impact on user-facing site performance
- Purging is selective (only affected URLs)

## Security Considerations

- **API Token Security**: Store tokens securely in environment variables
- **Token Permissions**: Use minimal required permissions (Zone:Read, Cache Purge:Edit)
- **Zone Restriction**: Limit token to specific zone only
- **Regular Rotation**: Consider rotating API tokens periodically

## Benefits

✅ **Instant Content Updates**: Users see changes immediately after sync
✅ **Selective Purging**: Only affected URLs are purged (efficient)
✅ **Automatic Process**: No manual intervention required
✅ **Fallback Support**: Works with or without deployment webhooks
✅ **Monitoring**: Clear logging and status reporting

## Advanced Configuration

### Custom URL Patterns

You can customize which URLs get purged by modifying `src/utils/cache.ts`:

```typescript
export function getCriticalUrls(baseUrl: string): string[] {
  return [
    `${baseUrl}/`,
    `${baseUrl}/products`,
    `${baseUrl}/your-custom-page`,
    // Add your custom URLs here
  ];
}
```

### Cache Tags (Future Enhancement)

For more advanced cache management, you can implement cache tags:

```typescript
// Purge by tags instead of URLs
await purgeCloudflareCache({
  zoneId,
  apiToken,
  tags: ['products', 'category-electronics']
});
```

This requires setting up cache tags in your Cloudflare configuration.
