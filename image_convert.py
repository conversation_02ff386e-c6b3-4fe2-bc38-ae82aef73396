import os
from PIL import Image

# Explicitly import AVIF plugin for Pillow
import pillow_avif  # noqa: F401

# Make sure you have installed: pip install pillow pillow-avif-plugin


def convert_images_to_webp(source_dir, dest_dir=None, extensions=None):
    """
    Batch convert images of specified types to WebP.
    :param source_dir: Directory with source images
    :param dest_dir: Output directory (defaults to source_dir)
    :param extensions: Iterable of file extensions to convert (default: common types)
    """
    if dest_dir is None:
        dest_dir = source_dir
    if extensions is None:
        extensions = ['.avif', '.jpg', '.jpeg', '.png', '.tiff', '.bmp', '.gif']
    for filename in os.listdir(source_dir):
        ext = os.path.splitext(filename)[1].lower()
        if ext in extensions:
            src_path = os.path.join(source_dir, filename)
            webp_filename = os.path.splitext(filename)[0] + '.webp'
            webp_path = os.path.join(dest_dir, webp_filename)
            try:
                with Image.open(src_path) as img:
                    img.save(webp_path, 'WEBP', quality=95)
                print(f"Converted: {src_path} -> {webp_path}")
            except Exception as e:
                print(f"Failed to convert {src_path}: {e}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) < 2:
        print("Usage: python image_convert.py <source_dir> [dest_dir] [ext1,ext2,...]")
        print("Example: python image_convert.py ./images ./webp jpg,png,avif")
    else:
        source = sys.argv[1]
        dest = sys.argv[2] if len(sys.argv) > 2 else None
        if len(sys.argv) > 3:
            exts = ['.' + e.strip().lower().lstrip('.') for e in sys.argv[3].split(',')]
        else:
            exts = None
        convert_images_to_webp(source, dest, exts)