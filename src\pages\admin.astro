---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import Header from '../components/Header.astro';
import ModernAdminPanel from '../components/ModernAdminPanel.astro';
import '../assets/css/admin.css';
---

<Layout
  title="Admin Panel | Cheers Marketplace"
  description="Administrative panel for managing Cheers Marketplace products and content."
  noIndex={true}
>
    <meta name="description" content="Admin panel for managing products" />
    <meta name="robots" content="noindex, nofollow" />
  </Fragment>

  <Header />

  <main class="admin-main">
    <ModernAdminPanel />
  </main>

  <footer>
    <div class="container">
      <p>Admin access only. <span aria-label="lock" role="img">🔒</span></p>
    </div>
  </footer>
</Layout>

<style>
  .admin-main {
    padding-top: 0;
    min-height: auto;
  }
</style>
