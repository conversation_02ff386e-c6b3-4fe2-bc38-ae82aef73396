---
// Product Info Component
export interface Props {
  name: string;
  category: string;
  condition: string;
  shortDescription?: string;
  description: string;
  displayPrice: string;
  keyPoints?: Array<{label: string; value: string}>;
}

const {
  name,
  category,
  condition,
  shortDescription,
  description,
  displayPrice,
  keyPoints
} = Astro.props;
---

<div class="product-info">
  <div class="product-header">
    <h3 class="product-name">{name}</h3>
    <div class="product-meta">
      <span class="product-category">{category}</span>
      <div class="condition-info">
        <span class="condition-label">Condition:</span>
        <span class={`condition-badge condition-${condition.toLowerCase()}`}>{condition}</span>
      </div>
    </div>
  </div>

  {shortDescription && (
    <p class="product-description">
      {shortDescription}{description.length > 100 ? '...' : ''}
    </p>
  )}

  <div class="product-footer">
    <div class="product-price">
      <span class="price-amount">{displayPrice}</span>
    </div>

    {keyPoints && keyPoints.length > 0 && (
      <div class="product-features">
        {keyPoints.slice(0, 2).map((kp) => (
          <span class="feature-badge" title={`${kp.label}: ${kp.value}`}>
            {kp.label}: {kp.value}
          </span>
        ))}
      </div>
    )}
  </div>
</div>

<style>
  .product-info {
    padding: 0.75rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .product-header {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .product-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  .condition-info {
    display: flex;
    align-items: center;
    gap: 0.375rem;
  }

  .condition-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .product-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text);
    margin: 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-category {
    font-size: 0.875rem;
    color: var(--text-secondary);
    text-transform: capitalize;
    font-weight: 500;
  }

  .condition-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border: 1px solid;
  }

  .condition-poor {
    background: #fef2f2;
    color: #991b1b;
    border-color: #fca5a5;
  }

  .condition-fair {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
  }

  .condition-good {
    background: #f0fdf4;
    color: #166534;
    border-color: #86efac;
  }

  .condition-excellent {
    background: #eff6ff;
    color: #1e40af;
    border-color: #93c5fd;
  }

  .condition-new {
    background: #faf5ff;
    color: #7c2d12;
    border-color: #c4b5fd;
  }

  .product-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .product-footer {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    gap: 0.75rem;
    margin-top: 0.5rem;
  }

  .product-price {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .price-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary);
  }

  .product-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.375rem;
    max-width: 50%;
  }

  .feature-badge {
    background: var(--light-background);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid var(--border);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 140px;
    line-height: 1.2;
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    .product-info {
      padding: 0.625rem;
      gap: 0.375rem;
    }

    .condition-info {
      gap: 0.25rem;
    }

    .condition-label {
      font-size: 0.6875rem;
    }

    .product-name {
      font-size: 1rem;
    }

    .product-category {
      font-size: 0.8125rem;
    }

    .product-description {
      font-size: 0.8125rem;
    }

    .price-amount {
      font-size: 1.125rem;
    }

    .product-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.375rem;
      margin-top: 0.375rem;
    }

    .product-features {
      max-width: 100%;
    }

    .feature-badge {
      font-size: 0.6875rem;
      padding: 0.1875rem 0.375rem;
      max-width: 120px;
    }
  }

  @media (max-width: 480px) {
    .product-info {
      padding: 0.5rem;
      gap: 0.25rem;
    }

    .product-name {
      font-size: 0.9375rem;
    }

    .price-amount {
      font-size: 1rem;
    }
  }
</style>
